# ================= نافذة مسك المصاريف =================
# ملف منفصل لإدارة المصاريف المالية للمؤسسة التعليمية
# يحتوي على: إدخال المصاريف، تصنيفها، البحث والتصفية، التقارير

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ExpenseManager:
    """مدير المصاريف"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول المصاريف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جدول المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ DATE NOT NULL,
                    المبلغ REAL NOT NULL,
                    نوع_المصروف TEXT NOT NULL,
                    الجهة_المستفيدة TEXT,
                    طريقة_الأداء TEXT,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول أنواع المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS أنواع_المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_النوع TEXT UNIQUE NOT NULL,
                    وصف_النوع TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)

          


            conn.commit()
            conn.close()

            print("تم إنشاء جداول المصاريف بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات المصاريف: {str(e)}")

class ExpenseManagementWindow(QMainWindow):
    """نافذة مسك المصاريف"""
    
    def __init__(self):
        super().__init__()
        self.manager = ExpenseManager()
        self.init_ui()
        self.load_expense_types()
        self.load_expenses()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💰 مسك المصاريف")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("💰 مسك المصاريف")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # مجموعة إدخال المصروف
        self.create_expense_form(main_layout)

        # مجموعة الأزرار
        self.create_buttons_group(main_layout)

        # جدول المصاريف
        self.create_expenses_table(main_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام مسك المصاريف")

        # تحميل البيانات الأولية
        self.load_expense_types()
        self.load_autocomplete_data()
        self.load_expenses()
    
    def create_expense_form(self, main_layout):
        """إنشاء نموذج إدخال المصروف"""
        form_group = QGroupBox("📝 إدخال مصروف جديد")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout = QVBoxLayout(form_group)

        # تقسيم الحقول إلى عمودين
        columns_layout = QHBoxLayout()

        # العمود الأول
        column1_layout = QVBoxLayout()

        # التاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 14, QFont.Bold))
        date_label.setFixedWidth(100)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 12, QFont.Bold))
        self.date_edit.setFixedWidth(120)
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        column1_layout.addLayout(date_layout)

        # المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ:")
        amount_label.setFont(QFont("Calibri", 14, QFont.Bold))
        amount_label.setFixedWidth(100)
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("أدخل المبلغ...")
        self.amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.amount_input.setFixedWidth(150)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(self.amount_input)
        amount_layout.addStretch()
        column1_layout.addLayout(amount_layout)

        # نوع المصروف
        type_layout = QHBoxLayout()
        type_label = QLabel("نوع المصروف:")
        type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        type_label.setFixedWidth(100)
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.expense_type_combo.setFixedWidth(300)
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.expense_type_combo)
        type_layout.addStretch()
        column1_layout.addLayout(type_layout)

        columns_layout.addLayout(column1_layout)

        # العمود الثاني
        column2_layout = QVBoxLayout()

        # الجهة المستفيدة
        beneficiary_layout = QHBoxLayout()
        beneficiary_label = QLabel("الجهة المستفيدة:")
        beneficiary_label.setFont(QFont("Calibri", 14, QFont.Bold))
        beneficiary_label.setFixedWidth(120)
        self.beneficiary_input = QLineEdit()
        self.beneficiary_input.setPlaceholderText("اسم الجهة...")
        self.beneficiary_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.beneficiary_input.setFixedWidth(300)

        # إعداد الإكمال التلقائي للجهة المستفيدة
        self.beneficiary_completer = QCompleter()
        self.beneficiary_completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.beneficiary_completer.setFilterMode(Qt.MatchContains)
        self.beneficiary_input.setCompleter(self.beneficiary_completer)
        beneficiary_layout.addWidget(beneficiary_label)
        beneficiary_layout.addWidget(self.beneficiary_input)
        beneficiary_layout.addStretch()
        column2_layout.addLayout(beneficiary_layout)

        # طريقة الأداء
        payment_layout = QHBoxLayout()
        payment_label = QLabel("طريقة الأداء:")
        payment_label.setFont(QFont("Calibri", 14, QFont.Bold))
        payment_label.setFixedWidth(120)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.payment_method_combo.setFixedWidth(150)
        payment_layout.addWidget(payment_label)
        payment_layout.addWidget(self.payment_method_combo)
        payment_layout.addStretch()
        column2_layout.addLayout(payment_layout)

        # الملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 14, QFont.Bold))
        notes_label.setFixedWidth(120)
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("ملاحظات...")
        self.notes_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.notes_input.setFixedWidth(300)

        # إعداد الإكمال التلقائي للملاحظات
        self.notes_completer = QCompleter()
        self.notes_completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.notes_completer.setFilterMode(Qt.MatchContains)
        self.notes_input.setCompleter(self.notes_completer)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_input)
        notes_layout.addStretch()
        column2_layout.addLayout(notes_layout)

        columns_layout.addLayout(column2_layout)
        form_layout.addLayout(columns_layout)
        main_layout.addWidget(form_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر إضافة المصروف
        add_btn = QPushButton("➕ إضافة المصروف")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_expense)
        buttons_layout.addWidget(add_btn)

        # زر مسح الحقول
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_expenses)
        buttons_layout.addWidget(refresh_btn)

        # زر تقرير سند الصرف
        receipt_btn = QPushButton("📄 سند الصرف")
        receipt_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        receipt_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        receipt_btn.clicked.connect(self.generate_expense_receipt)
        buttons_layout.addWidget(receipt_btn)

        # زر إضافة نوع مصروف
        add_type_btn = QPushButton("➕ إضافة نوع")
        add_type_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #1aa179;
            }
        """)
        add_type_btn.clicked.connect(self.add_expense_type)
        buttons_layout.addWidget(add_type_btn)

        # زر حذف المصاريف المحددة
        delete_selected_btn = QPushButton("🗑️ حذف المحدد")
        delete_selected_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_selected_btn.clicked.connect(self.delete_selected_expenses)
        buttons_layout.addWidget(delete_selected_btn)

        main_layout.addWidget(buttons_group)

    def create_expenses_table(self, main_layout):
        """إنشاء جدول المصاريف"""
        # مجموعة البحث والتصفية
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)

        # البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في المصاريف...")
        self.search_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.search_input.textChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.search_input)

        # فلتر النوع
        type_filter_label = QLabel("النوع:")
        type_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(type_filter_label)

        self.type_filter_combo = QComboBox()
        self.type_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.type_filter_combo.currentTextChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.type_filter_combo)

        # فلتر الشهر
        month_filter_label = QLabel("الشهر:")
        month_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(month_filter_label)

        self.month_filter_combo = QComboBox()
        months = ["جميع الشهور", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_filter_combo.addItems(months)
        self.month_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.month_filter_combo.currentTextChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.month_filter_combo)

        main_layout.addWidget(search_group)

        # جدول المصاريف
        table_group = QGroupBox("📊 سجل المصاريف")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.expenses_table.setHorizontalHeaderLabels([
            "ID", "التاريخ", "المبلغ", "نوع المصروف",
            "الجهة المستفيدة", "طريقة الأداء", "ملاحظات"
        ])

        # تحديد عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # تحديد عرض الأعمدة حسب المطلوب
        self.expenses_table.setColumnWidth(0, 50)   # ID
        self.expenses_table.setColumnWidth(1, 120)  # التاريخ
        self.expenses_table.setColumnWidth(2, 150)  # المبلغ
        self.expenses_table.setColumnWidth(3, 200)  # نوع المصروف
        self.expenses_table.setColumnWidth(4, 200)  # الجهة المستفيدة
        self.expenses_table.setColumnWidth(5, 150)  # طريقة الأداء
        self.expenses_table.setColumnWidth(6, 200)  # ملاحظات

        # تنسيق الجدول
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setSelectionMode(QAbstractItemView.MultiSelection)  # تحديد متعدد
        self.expenses_table.setSortingEnabled(True)

        # تنسيق الجدول
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إضافة قائمة سياق للجدول
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.expenses_table)
        main_layout.addWidget(table_group)



    def load_expense_types(self):
        """تحميل أنواع المصاريف"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT اسم_النوع FROM أنواع_المصاريف WHERE نشط = 1 ORDER BY اسم_النوع")
            types = cursor.fetchall()

            self.expense_type_combo.clear()
            self.type_filter_combo.clear()
            self.type_filter_combo.addItem("جميع الأنواع")

            for type_row in types:
                type_name = type_row[0]
                self.expense_type_combo.addItem(type_name)
                self.type_filter_combo.addItem(type_name)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل أنواع المصاريف: {str(e)}")
            # إضافة أنواع افتراضية في حالة الخطأ
            default_types = ["رواتب", "كراء", "فواتير", "معدات", "صيانة", "إعلانات", "قرطاسية", "نقل", "تأمين", "أخرى"]
            self.expense_type_combo.addItems(default_types)
            self.type_filter_combo.addItems(default_types)

    def load_expenses(self):
        """تحميل المصاريف في الجدول"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة,
                       طريقة_الأداء, ملاحظات
                FROM المصاريف
                ORDER BY التاريخ DESC, id DESC
            """)
            expenses = cursor.fetchall()

            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                for col, value in enumerate(expense):
                    if col == 2:  # المبلغ
                        item = QTableWidgetItem(f"{float(value):,.2f}")
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFont(QFont("Calibri", 12, QFont.Bold))
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFont(QFont("Calibri", 12, QFont.Bold))

                    self.expenses_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(expenses)} مصروف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المصاريف: {str(e)}")
            self.expenses_table.setRowCount(0)
            self.statusBar().showMessage("لا توجد مصاريف")

        # تحديث بيانات الإكمال التلقائي
        self.load_autocomplete_data()

    def load_autocomplete_data(self):
        """تحميل بيانات الإكمال التلقائي للجهة المستفيدة والملاحظات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # جلب الجهات المستفيدة الفريدة
            cursor.execute("""
                SELECT DISTINCT الجهة_المستفيدة
                FROM المصاريف
                WHERE الجهة_المستفيدة IS NOT NULL AND الجهة_المستفيدة != ''
                ORDER BY الجهة_المستفيدة
            """)
            beneficiaries = [row[0] for row in cursor.fetchall()]

            # جلب الملاحظات الفريدة
            cursor.execute("""
                SELECT DISTINCT ملاحظات
                FROM المصاريف
                WHERE ملاحظات IS NOT NULL AND ملاحظات != ''
                ORDER BY ملاحظات
            """)
            notes = [row[0] for row in cursor.fetchall()]

            conn.close()

            # تحديث نماذج الإكمال التلقائي
            from PyQt5.QtCore import QStringListModel

            beneficiary_model = QStringListModel(beneficiaries)
            self.beneficiary_completer.setModel(beneficiary_model)

            notes_model = QStringListModel(notes)
            self.notes_completer.setModel(notes_model)

        except Exception as e:
            print(f"خطأ في تحميل بيانات الإكمال التلقائي: {str(e)}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ")
                return

            if not self.expense_type_combo.currentText():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع المصروف")
                return

            try:
                amount = float(self.amount_input.text().strip())
                if amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # إدراج المصروف في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO المصاريف (التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, طريقة_الأداء, ملاحظات)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                self.date_edit.date().toString("yyyy-MM-dd"),
                amount,
                self.expense_type_combo.currentText(),
                self.beneficiary_input.text().strip(),
                self.payment_method_combo.currentText(),
                self.notes_input.text().strip()
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")
            self.clear_form()
            self.load_expenses()
            self.load_autocomplete_data()  # تحديث بيانات الإكمال التلقائي

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المصروف: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.date_edit.setDate(QDate.currentDate())
        self.amount_input.clear()
        if self.expense_type_combo.count() > 0:
            self.expense_type_combo.setCurrentIndex(0)
        self.beneficiary_input.clear()
        self.payment_method_combo.setCurrentIndex(0)
        self.notes_input.clear()

    def filter_expenses(self):
        """تصفية المصاريف"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()
        month_filter = self.month_filter_combo.currentText()

        for row in range(self.expenses_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.expenses_table.columnCount()):
                    item = self.expenses_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع الأنواع":
                type_item = self.expenses_table.item(row, 3)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            # فلتر الشهر
            if month_filter and month_filter != "جميع الشهور":
                date_item = self.expenses_table.item(row, 1)
                if date_item:
                    try:
                        date_str = date_item.text()
                        if date_str:
                            # تحويل التاريخ لاستخراج الشهر
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                            month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                            expense_month = month_names[date_obj.month]
                            if expense_month != month_filter:
                                show_row = False
                    except:
                        pass

            self.expenses_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.expenses_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.expenses_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_expense()
        elif action == delete_action:
            self.delete_expense()

    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات المصروف
        expense_id = self.expenses_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM المصاريف WHERE id = ?", (expense_id,))
            expense = cursor.fetchone()

            if expense:
                # تعبئة النموذج بالبيانات الحالية
                self.date_edit.setDate(QDate.fromString(expense[1], "yyyy-MM-dd"))
                self.amount_input.setText(str(expense[2]))

                # البحث عن نوع المصروف في القائمة
                type_index = self.expense_type_combo.findText(expense[3])
                if type_index >= 0:
                    self.expense_type_combo.setCurrentIndex(type_index)

                self.beneficiary_input.setText(expense[4] or "")

                # البحث عن طريقة الأداء في القائمة
                payment_index = self.payment_method_combo.findText(expense[5])
                if payment_index >= 0:
                    self.payment_method_combo.setCurrentIndex(payment_index)

                self.notes_input.setText(expense[6] or "")

                # حذف المصروف القديم
                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات المصروف للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة المصروف'.")

            conn.close()
            self.load_expenses()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل المصروف: {str(e)}")

    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        expense_id = self.expenses_table.item(current_row, 0).text()
        expense_amount = self.expenses_table.item(current_row, 2).text()
        expense_type = self.expenses_table.item(current_row, 3).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا المصروف؟\n\nالنوع: {expense_type}\nالمبلغ: {expense_amount}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف المصروف بنجاح")
                self.load_expenses()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")

    def add_expense_type(self):
        """فتح نافذة إدارة أنواع المصاريف"""
        self.expense_types_window = ExpenseTypesWindow(self)
        self.expense_types_window.show()

    def delete_selected_expenses(self):
        """حذف المصاريف المحددة"""
        selected_rows = set()
        for item in self.expenses_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف أو أكثر للحذف")
            return

        # جمع معلومات المصاريف المحددة
        expenses_info = []
        expense_ids = []
        for row in selected_rows:
            expense_id = self.expenses_table.item(row, 0).text()
            expense_type = self.expenses_table.item(row, 3).text()
            expense_amount = self.expenses_table.item(row, 2).text()
            expenses_info.append(f"- {expense_type}: {expense_amount}")
            expense_ids.append(expense_id)

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف {len(selected_rows)} مصروف؟\n\n" + "\n".join(expenses_info),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                # حذف المصاريف المحددة
                placeholders = ','.join(['?' for _ in expense_ids])
                cursor.execute(f"DELETE FROM المصاريف WHERE id IN ({placeholders})", expense_ids)

                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", f"تم حذف {len(selected_rows)} مصروف بنجاح")
                self.load_expenses()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصاريف: {str(e)}")

    def generate_expense_receipt(self):
        """إنشاء تقرير سند الصرف PDF"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف من الجدول لإنشاء سند الصرف")
            return

        try:
            # جلب بيانات المصروف المحدد
            expense_id = self.expenses_table.item(current_row, 0).text()
            expense_date = self.expenses_table.item(current_row, 1).text()
            expense_amount = self.expenses_table.item(current_row, 2).text()
            expense_type = self.expenses_table.item(current_row, 3).text()
            expense_beneficiary = self.expenses_table.item(current_row, 4).text()
            expense_method = self.expenses_table.item(current_row, 5).text()
            expense_notes = self.expenses_table.item(current_row, 6).text()

            # جلب بيانات المؤسسة من قاعدة البيانات
            logo_path = None
            institution_name = "المؤسسة التعليمية"

            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                # التحقق من وجود الجدول أولاً
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
                if cursor.fetchone():
                    cursor.execute("SELECT ImagePath1, المؤسسة FROM بيانات_المؤسسة LIMIT 1")
                    institution_row = cursor.fetchone()

                    if institution_row:
                        if institution_row[0]:
                            # التحقق من وجود ملف الشعار
                            import os
                            if os.path.exists(str(institution_row[0])):
                                logo_path = str(institution_row[0])
                                print(f"تم العثور على الشعار: {logo_path}")
                            else:
                                print(f"ملف الشعار غير موجود: {institution_row[0]}")

                        if institution_row[1]:
                            institution_name = str(institution_row[1])
                            print(f"اسم المؤسسة: {institution_name}")
                    else:
                        print("لا توجد بيانات في جدول بيانات_المؤسسة")
                else:
                    print("جدول بيانات_المؤسسة غير موجود")

                conn.close()
            except Exception as db_error:
                print(f"خطأ في جلب بيانات المؤسسة: {db_error}")
                # استخدام القيم الافتراضية

            # استيراد مكتبات PDF العربية
            try:
                from fpdf import FPDF
                import arabic_reshaper
                from bidi.algorithm import get_display
            except ImportError as import_error:
                QMessageBox.critical(self, "خطأ", f"مكتبات PDF العربية غير مثبتة:\n{import_error}\n\nيرجى تثبيتها باستخدام:\npip install fpdf2 arabic-reshaper python-bidi")
                return

            # تحديد مسار الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ سند الصرف",
                f"سند_صرف_{expense_id}_{expense_date}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # إنشاء فئة PDF عربية مثل print101.py
            class ArabicPDF(FPDF):
                def __init__(self):
                    super().__init__('P', 'mm', 'A4')
                    self.set_margins(10, 10, 10)
                    self.set_auto_page_break(auto=True, margin=10)

                    # إضافة خط عربي إذا كان متوفراً
                    import os
                    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
                    arial_path = os.path.join(fonts_dir, 'arial.ttf')
                    arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')

                    try:
                        if os.path.exists(arial_path):
                            self.add_font('Arial', '', arial_path)
                            self.arabic_available = True
                        else:
                            self.arabic_available = False

                        if os.path.exists(arial_bold_path):
                            self.add_font('Arial', 'B', arial_bold_path)
                            self.arabic_bold_available = True
                        else:
                            self.arabic_bold_available = False
                    except:
                        self.arabic_available = False
                        self.arabic_bold_available = False

                    # تعيين الخط الافتراضي
                    if self.arabic_available:
                        self.set_font('Arial', '', 12)
                    else:
                        self.set_font('helvetica', '', 12)

                def ar_text(self, txt: str) -> str:
                    """تحويل النص العربي ليتم عرضه بشكل صحيح"""
                    try:
                        reshaped = arabic_reshaper.reshape(str(txt))
                        return get_display(reshaped)
                    except Exception:
                        # في حالة فشل التحويل، إرجاع النص كما هو
                        return str(txt)

                def set_title_font(self, size=15):
                    """تعيين خط العناوين"""
                    if self.arabic_bold_available:
                        self.set_font('Arial', 'B', size)
                    else:
                        self.set_font('helvetica', 'B', size)

                def set_detail_font(self, size=13):
                    """تعيين خط التفاصيل"""
                    if self.arabic_bold_available:
                        self.set_font('Arial', 'B', size)
                    else:
                        self.set_font('helvetica', 'B', size)

                def set_normal_font(self, size=12):
                    """تعيين الخط العادي"""
                    if self.arabic_available:
                        self.set_font('Arial', '', size)
                    else:
                        self.set_font('helvetica', '', size)

            # إنشاء المستند
            pdf = ArabicPDF()
            pdf.add_page()

            # شعار المؤسسة
            if logo_path:
                try:
                    # حساب موضع الشعار في المنتصف
                    logo_width = 40
                    logo_height = 20
                    x_logo = (pdf.w - logo_width) / 2
                    pdf.image(logo_path, x=x_logo, y=pdf.get_y(), w=logo_width, h=logo_height)
                    pdf.ln(25)
                except Exception as img_error:
                    print(f"خطأ في إضافة الشعار: {img_error}")
                    # استخدام رمز تعبيري كبديل
                    pdf.set_title_font(18)
                    pdf.set_text_color(0, 51, 102)
                    pdf.cell(0, 15, pdf.ar_text("🏫"), border=0, align='C')
                    pdf.ln(20)
            else:
                # استخدام رمز تعبيري كبديل
                pdf.set_title_font(18)
                pdf.set_text_color(0, 51, 102)
                pdf.cell(0, 15, pdf.ar_text("🏫"), border=0, align='C')
                pdf.ln(20)

            # اسم المؤسسة
            pdf.set_title_font(16)
            pdf.set_text_color(0, 51, 102)
            pdf.cell(0, 15, pdf.ar_text(institution_name), border=0, align='C')
            pdf.ln(15)

            # عنوان التقرير
            pdf.set_title_font(16)
            pdf.cell(0, 15, pdf.ar_text("سند الصرف"), border=0, align='C')
            pdf.ln(20)

            # إعادة تعيين لون النص
            pdf.set_text_color(0, 0, 0)
            pdf.set_detail_font(12)

            # بيانات السند - عكس ترتيب الأعمدة
            receipt_data = [
                [expense_id, "رقم السند:"],
                [expense_date, "التاريخ:"],
                [expense_amount + " درهم", "المبلغ:"],
                [expense_type, "نوع المصروف:"],
                [expense_beneficiary, "الجهة المستفيدة:"],
                [expense_method, "طريقة الأداء:"],
                [expense_notes if expense_notes else "لا توجد", "ملاحظات:"]
            ]

            # رسم جدول البيانات - مع عكس ترتيب الأعمدة
            for i, (value, label) in enumerate(receipt_data):
                y = pdf.get_y()

                # خلفية متناوبة
                if i % 2 == 0:
                    pdf.set_fill_color(240, 240, 240)
                else:
                    pdf.set_fill_color(255, 255, 255)

                # رسم الخلية الأولى (القيمة)
                pdf.set_xy(10, y)
                pdf.cell(130, 10, pdf.ar_text(str(value)), border=1, align='R', fill=True)

                # رسم الخلية الثانية (التسمية)
                pdf.set_xy(140, y)
                pdf.cell(60, 10, pdf.ar_text(label), border=1, align='R', fill=True)
                pdf.ln(10)

            pdf.ln(20)

            # التوقيع والتاريخ
            current_date = datetime.now().strftime('%Y-%m-%d')

            # خط التوقيع
            pdf.set_xy(10, pdf.get_y())
            pdf.cell(90, 10, pdf.ar_text("التوقيع: ________________"), border=0, align='C')

            # التاريخ
            pdf.set_xy(110, pdf.get_y())
            pdf.cell(90, 10, pdf.ar_text(f"التاريخ: {current_date}"), border=0, align='C')

            # حفظ الملف
            try:
                pdf.output(file_path)
                QMessageBox.information(self, "نجح", f"تم إنشاء سند الصرف بنجاح:\n{file_path}")

                # فتح الملف
                try:
                    import os
                    os.startfile(file_path)
                except Exception as open_error:
                    print(f"تعذر فتح الملف: {open_error}")

            except Exception as save_error:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الملف:\n{save_error}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء سند الصرف: {str(e)}")


class ExpenseTypesWindow(QDialog):
    """نافذة إدارة أنواع المصاريف"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.manager = parent.manager
        self.init_ui()
        self.load_expense_types()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة أنواع المصاريف")
        self.setFixedSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        self.center_window()

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # العنوان
        title_label = QLabel("إدارة أنواع المصاريف")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # نموذج إضافة نوع جديد
        self.create_add_form(main_layout)

        # جدول أنواع المصاريف
        self.create_types_table(main_layout)

        # أزرار العمليات
        self.create_action_buttons(main_layout)

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def create_add_form(self, main_layout):
        """إنشاء نموذج إضافة نوع جديد"""
        form_group = QGroupBox("إضافة نوع مصروف جديد")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout = QHBoxLayout(form_group)

        # اسم النوع
        name_label = QLabel("اسم النوع:")
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(name_label)

        self.type_name_input = QLineEdit()
        self.type_name_input.setPlaceholderText("أدخل اسم نوع المصروف...")
        self.type_name_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.type_name_input)

        # وصف النوع
        desc_label = QLabel("الوصف:")
        desc_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(desc_label)

        self.type_desc_input = QLineEdit()
        self.type_desc_input.setPlaceholderText("وصف النوع (اختياري)...")
        self.type_desc_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.type_desc_input)

        # زر إضافة
        add_btn = QPushButton("➕ إضافة")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_new_type)
        form_layout.addWidget(add_btn)

        main_layout.addWidget(form_group)

    def create_types_table(self, main_layout):
        """إنشاء جدول أنواع المصاريف"""
        table_group = QGroupBox("أنواع المصاريف الموجودة")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.types_table = QTableWidget()
        self.types_table.setColumnCount(4)
        self.types_table.setHorizontalHeaderLabels(["ID", "اسم النوع", "الوصف", "نشط"])

        # تنسيق رؤوس الجدول
        header = self.types_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # تحديد عرض الأعمدة
        self.types_table.setColumnWidth(0, 60)   # ID
        self.types_table.setColumnWidth(1, 250)  # اسم النوع
        self.types_table.setColumnWidth(2, 350)  # الوصف
        self.types_table.setColumnWidth(3, 100)  # نشط

        # تنسيق الجدول
        self.types_table.setAlternatingRowColors(True)
        self.types_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.types_table.setSelectionMode(QAbstractItemView.SingleSelection)

        table_layout.addWidget(self.types_table)
        main_layout.addWidget(table_group)

    def create_action_buttons(self, main_layout):
        """إنشاء أزرار العمليات"""
        buttons_group = QGroupBox("العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر تعديل
        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        edit_btn.clicked.connect(self.edit_selected_type)
        buttons_layout.addWidget(edit_btn)

        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_btn.clicked.connect(self.delete_selected_type)
        buttons_layout.addWidget(delete_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        refresh_btn.clicked.connect(self.load_expense_types)
        buttons_layout.addWidget(refresh_btn)

        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        main_layout.addWidget(buttons_group)

    def load_expense_types(self):
        """تحميل أنواع المصاريف في الجدول"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='أنواع_المصاريف'")
            if not cursor.fetchone():
                QMessageBox.warning(self, "تحذير", "جدول أنواع المصاريف غير موجود")
                conn.close()
                return

            cursor.execute("""
                SELECT id, اسم_النوع, وصف_النوع, نشط
                FROM أنواع_المصاريف
                ORDER BY نشط DESC, اسم_النوع
            """)
            types = cursor.fetchall()

            self.types_table.setRowCount(len(types))

            for row, type_data in enumerate(types):
                for col, value in enumerate(type_data):
                    if col == 3:  # عمود نشط
                        item = QTableWidgetItem("نعم" if value else "لا")
                        # تلوين الصف حسب حالة التفعيل
                        if not value:  # غير نشط
                            item.setBackground(QColor("#ffcccc"))
                    else:
                        item = QTableWidgetItem(str(value) if value is not None else "")
                        # تلوين الصف حسب حالة التفعيل
                        if not type_data[3]:  # غير نشط
                            item.setBackground(QColor("#ffcccc"))

                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 12, QFont.Bold))
                    self.types_table.setItem(row, col, item)

            conn.close()
            print(f"تم تحميل {len(types)} نوع مصروف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل أنواع المصاريف: {str(e)}")
            print(f"تفاصيل خطأ التحميل: {e}")  # للتشخيص

    def add_new_type(self):
        """إضافة نوع مصروف جديد"""
        type_name = self.type_name_input.text().strip()
        type_desc = self.type_desc_input.text().strip()

        if not type_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم النوع")
            return

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من عدم وجود النوع مسبقاً
            cursor.execute("SELECT COUNT(*) FROM أنواع_المصاريف WHERE اسم_النوع = ?", (type_name,))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذا النوع موجود مسبقاً!")
                conn.close()
                return

            # إضافة النوع الجديد
            cursor.execute("""
                INSERT INTO أنواع_المصاريف (اسم_النوع, وصف_النوع, نشط)
                VALUES (?, ?, 1)
            """, (type_name, type_desc if type_desc else f"نوع مصروف: {type_name}"))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", f"تم إضافة نوع المصروف '{type_name}' بنجاح")

            # مسح الحقول وتحديث الجدول
            self.type_name_input.clear()
            self.type_desc_input.clear()
            self.load_expense_types()

            # تحديث القائمة في النافذة الرئيسية
            if self.parent:
                self.parent.load_expense_types()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة نوع المصروف: {str(e)}")

    def edit_selected_type(self):
        """تعديل نوع المصروف المحدد"""
        current_row = self.types_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع مصروف للتعديل")
            return

        try:
            # التأكد من وجود البيانات في الصف المحدد
            type_id_item = self.types_table.item(current_row, 0)
            type_name_item = self.types_table.item(current_row, 1)
            type_desc_item = self.types_table.item(current_row, 2)

            if not type_id_item or not type_name_item:
                QMessageBox.warning(self, "تحذير", "خطأ في قراءة بيانات النوع المحدد")
                return

            type_id = type_id_item.text()
            current_name = type_name_item.text()
            current_desc = type_desc_item.text() if type_desc_item else ""

            # نافذة تعديل
            new_name, ok1 = QInputDialog.getText(
                self, "تعديل اسم النوع",
                "اسم النوع:", QLineEdit.Normal, current_name
            )

            if ok1 and new_name.strip():
                new_desc, ok2 = QInputDialog.getText(
                    self, "تعديل وصف النوع",
                    "وصف النوع:", QLineEdit.Normal, current_desc
                )

                if ok2:
                    conn = sqlite3.connect(self.manager.db_path)
                    cursor = conn.cursor()

                    # التحقق من عدم تكرار الاسم
                    cursor.execute("""
                        SELECT COUNT(*) FROM أنواع_المصاريف
                        WHERE اسم_النوع = ? AND id != ?
                    """, (new_name.strip(), type_id))

                    if cursor.fetchone()[0] > 0:
                        QMessageBox.warning(self, "تحذير", "هذا الاسم موجود مسبقاً!")
                        conn.close()
                        return

                    # تحديث النوع
                    cursor.execute("""
                        UPDATE أنواع_المصاريف
                        SET اسم_النوع = ?, وصف_النوع = ?
                        WHERE id = ?
                    """, (new_name.strip(), new_desc.strip(), type_id))

                    if cursor.rowcount > 0:
                        conn.commit()
                        QMessageBox.information(self, "نجح", "تم تعديل نوع المصروف بنجاح")
                        self.load_expense_types()

                        # تحديث القائمة في النافذة الرئيسية
                        if self.parent:
                            self.parent.load_expense_types()
                    else:
                        QMessageBox.warning(self, "تحذير", "فشل في تحديث النوع")

                    conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل نوع المصروف: {str(e)}")
            print(f"تفاصيل خطأ التعديل: {e}")  # للتشخيص

    def delete_selected_type(self):
        """حذف نوع المصروف المحدد"""
        current_row = self.types_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع مصروف للحذف")
            return

        try:
            # التأكد من وجود البيانات في الصف المحدد
            type_id_item = self.types_table.item(current_row, 0)
            type_name_item = self.types_table.item(current_row, 1)

            if not type_id_item or not type_name_item:
                QMessageBox.warning(self, "تحذير", "خطأ في قراءة بيانات النوع المحدد")
                return

            type_id = type_id_item.text()
            type_name = type_name_item.text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف نوع المصروف '{type_name}'؟\n\nملاحظة: سيتم إلغاء تفعيل النوع وليس حذفه نهائياً",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                # التحقق من وجود النوع في قاعدة البيانات
                cursor.execute("SELECT COUNT(*) FROM أنواع_المصاريف WHERE id = ?", (type_id,))
                if cursor.fetchone()[0] == 0:
                    QMessageBox.warning(self, "تحذير", "النوع المحدد غير موجود في قاعدة البيانات")
                    conn.close()
                    return

                # إلغاء تفعيل النوع بدلاً من حذفه
                cursor.execute("UPDATE أنواع_المصاريف SET نشط = 0 WHERE id = ?", (type_id,))

                if cursor.rowcount > 0:
                    conn.commit()
                    QMessageBox.information(self, "تم", f"تم إلغاء تفعيل نوع المصروف '{type_name}' بنجاح")
                    self.load_expense_types()

                    # تحديث القائمة في النافذة الرئيسية
                    if self.parent:
                        self.parent.load_expense_types()
                else:
                    QMessageBox.warning(self, "تحذير", "فشل في تحديث النوع")

                conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف نوع المصروف: {str(e)}")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = ExpenseManagementWindow()
    window.show()

    sys.exit(app.exec_())
