# ================= نافذة مسك المصاريف =================
# ملف منفصل لإدارة المصاريف المالية للمؤسسة التعليمية
# يحتوي على: إدخال المصاريف، تصنيفها، البحث والتصفية، التقارير

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ExpenseManager:
    """مدير المصاريف"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول المصاريف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جدول المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ DATE NOT NULL,
                    المبلغ REAL NOT NULL,
                    نوع_المصروف TEXT NOT NULL,
                    الجهة_المستفيدة TEXT,
                    طريقة_الأداء TEXT,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول أنواع المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS أنواع_المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_النوع TEXT UNIQUE NOT NULL,
                    وصف_النوع TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)

            # إدراج أنواع المصاريف الافتراضية
            default_expense_types = [
                ('رواتب', 'رواتب الموظفين والمدرسين'),
                ('كراء', 'إيجار المباني والمرافق'),
                ('فواتير', 'فواتير الكهرباء والماء والهاتف والإنترنت'),
                ('معدات', 'شراء المعدات والأجهزة التعليمية'),
                ('صيانة', 'صيانة المباني والمعدات'),
                ('إعلانات', 'الدعاية والإعلان والتسويق'),
                ('قرطاسية', 'المواد المكتبية والقرطاسية'),
                ('نقل', 'مصاريف النقل والمواصلات'),
                ('تأمين', 'أقساط التأمين المختلفة'),
                ('ضرائب', 'الضرائب والرسوم الحكومية'),
                ('أخرى', 'مصاريف متنوعة أخرى')
            ]

            cursor.executemany("""
                INSERT OR IGNORE INTO أنواع_المصاريف (اسم_النوع, وصف_النوع)
                VALUES (?, ?)
            """, default_expense_types)

            conn.commit()
            conn.close()

            print("تم إنشاء جداول المصاريف بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات المصاريف: {str(e)}")

class ExpenseManagementWindow(QMainWindow):
    """نافذة مسك المصاريف"""
    
    def __init__(self):
        super().__init__()
        self.manager = ExpenseManager()
        self.init_ui()
        self.load_expense_types()
        self.load_expenses()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💰 مسك المصاريف")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("💰 مسك المصاريف")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # مجموعة إدخال المصروف
        self.create_expense_form(main_layout)

        # مجموعة الأزرار
        self.create_buttons_group(main_layout)

        # جدول المصاريف
        self.create_expenses_table(main_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام مسك المصاريف")
    
    def create_expense_form(self, main_layout):
        """إنشاء نموذج إدخال المصروف"""
        form_group = QGroupBox("📝 إدخال مصروف جديد")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout = QVBoxLayout(form_group)

        # تقسيم الحقول إلى عمودين
        columns_layout = QHBoxLayout()

        # العمود الأول
        column1_layout = QVBoxLayout()

        # التاريخ
        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 14, QFont.Bold))
        date_label.setFixedWidth(100)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 12, QFont.Bold))
        self.date_edit.setFixedWidth(100)
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_edit)
        date_layout.addStretch()
        column1_layout.addLayout(date_layout)

        # المبلغ
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ:")
        amount_label.setFont(QFont("Calibri", 14, QFont.Bold))
        amount_label.setFixedWidth(100)
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("أدخل المبلغ...")
        self.amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.amount_input.setFixedWidth(160)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(self.amount_input)
        amount_layout.addStretch()
        column1_layout.addLayout(amount_layout)

        # نوع المصروف
        type_layout = QHBoxLayout()
        type_label = QLabel("نوع المصروف:")
        type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        type_label.setFixedWidth(100)
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.expense_type_combo.setFixedWidth(170)
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.expense_type_combo)
        type_layout.addStretch()
        column1_layout.addLayout(type_layout)

        columns_layout.addLayout(column1_layout)

        # العمود الثاني
        column2_layout = QVBoxLayout()

        # الجهة المستفيدة
        beneficiary_layout = QHBoxLayout()
        beneficiary_label = QLabel("الجهة المستفيدة:")
        beneficiary_label.setFont(QFont("Calibri", 14, QFont.Bold))
        beneficiary_label.setFixedWidth(120)
        self.beneficiary_input = QLineEdit()
        self.beneficiary_input.setPlaceholderText("اسم الجهة...")
        self.beneficiary_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.beneficiary_input.setFixedWidth(160)
        beneficiary_layout.addWidget(beneficiary_label)
        beneficiary_layout.addWidget(self.beneficiary_input)
        beneficiary_layout.addStretch()
        column2_layout.addLayout(beneficiary_layout)

        # طريقة الأداء
        payment_layout = QHBoxLayout()
        payment_label = QLabel("طريقة الأداء:")
        payment_label.setFont(QFont("Calibri", 14, QFont.Bold))
        payment_label.setFixedWidth(120)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.payment_method_combo.setFixedWidth(150)
        payment_layout.addWidget(payment_label)
        payment_layout.addWidget(self.payment_method_combo)
        payment_layout.addStretch()
        column2_layout.addLayout(payment_layout)

        # الملاحظات
        notes_layout = QHBoxLayout()
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 14, QFont.Bold))
        notes_label.setFixedWidth(120)
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("ملاحظات...")
        self.notes_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.notes_input.setFixedWidth(200)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_input)
        notes_layout.addStretch()
        column2_layout.addLayout(notes_layout)

        columns_layout.addLayout(column2_layout)
        form_layout.addLayout(columns_layout)
        main_layout.addWidget(form_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر إضافة المصروف
        add_btn = QPushButton("➕ إضافة المصروف")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_expense)
        buttons_layout.addWidget(add_btn)

        # زر مسح الحقول
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_expenses)
        buttons_layout.addWidget(refresh_btn)

        # زر تقرير سند الصرف
        receipt_btn = QPushButton("📄 سند الصرف")
        receipt_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        receipt_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        receipt_btn.clicked.connect(self.generate_expense_receipt)
        buttons_layout.addWidget(receipt_btn)

        main_layout.addWidget(buttons_group)

    def create_expenses_table(self, main_layout):
        """إنشاء جدول المصاريف"""
        # مجموعة البحث والتصفية
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)

        # البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في المصاريف...")
        self.search_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.search_input.textChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.search_input)

        # فلتر النوع
        type_filter_label = QLabel("النوع:")
        type_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(type_filter_label)

        self.type_filter_combo = QComboBox()
        self.type_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.type_filter_combo.currentTextChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.type_filter_combo)

        # فلتر الشهر
        month_filter_label = QLabel("الشهر:")
        month_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(month_filter_label)

        self.month_filter_combo = QComboBox()
        months = ["جميع الشهور", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_filter_combo.addItems(months)
        self.month_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.month_filter_combo.currentTextChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.month_filter_combo)

        main_layout.addWidget(search_group)

        # جدول المصاريف
        table_group = QGroupBox("📊 سجل المصاريف")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.expenses_table.setHorizontalHeaderLabels([
            "ID", "التاريخ", "المبلغ", "نوع المصروف",
            "الجهة المستفيدة", "طريقة الأداء", "ملاحظات"
        ])

        # تحديد عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # تحديد عرض الأعمدة حسب المطلوب
        self.expenses_table.setColumnWidth(0, 50)   # ID
        self.expenses_table.setColumnWidth(1, 100)  # التاريخ
        self.expenses_table.setColumnWidth(2, 160)  # المبلغ
        self.expenses_table.setColumnWidth(3, 170)  # نوع المصروف
        self.expenses_table.setColumnWidth(4, 160)  # الجهة المستفيدة
        self.expenses_table.setColumnWidth(5, 150)  # طريقة الأداء
        self.expenses_table.setColumnWidth(6, 200)  # ملاحظات

        # تنسيق الجدول
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.expenses_table.setSortingEnabled(True)

        # تنسيق الجدول
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إضافة قائمة سياق للجدول
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.expenses_table)
        main_layout.addWidget(table_group)



    def load_expense_types(self):
        """تحميل أنواع المصاريف"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT اسم_النوع FROM أنواع_المصاريف WHERE نشط = 1 ORDER BY اسم_النوع")
            types = cursor.fetchall()

            self.expense_type_combo.clear()
            self.type_filter_combo.clear()
            self.type_filter_combo.addItem("جميع الأنواع")

            for type_row in types:
                type_name = type_row[0]
                self.expense_type_combo.addItem(type_name)
                self.type_filter_combo.addItem(type_name)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل أنواع المصاريف: {str(e)}")
            # إضافة أنواع افتراضية في حالة الخطأ
            default_types = ["رواتب", "كراء", "فواتير", "معدات", "صيانة", "إعلانات", "قرطاسية", "نقل", "تأمين", "أخرى"]
            self.expense_type_combo.addItems(default_types)
            self.type_filter_combo.addItems(default_types)

    def load_expenses(self):
        """تحميل المصاريف في الجدول"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة,
                       طريقة_الأداء, ملاحظات
                FROM المصاريف
                ORDER BY التاريخ DESC, id DESC
            """)
            expenses = cursor.fetchall()

            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                for col, value in enumerate(expense):
                    if col == 2:  # المبلغ
                        item = QTableWidgetItem(f"{float(value):,.2f}")
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFont(QFont("Calibri", 12, QFont.Bold))
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFont(QFont("Calibri", 12, QFont.Bold))

                    self.expenses_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(expenses)} مصروف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المصاريف: {str(e)}")
            self.expenses_table.setRowCount(0)
            self.statusBar().showMessage("لا توجد مصاريف")

    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ")
                return

            if not self.expense_type_combo.currentText():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع المصروف")
                return

            try:
                amount = float(self.amount_input.text().strip())
                if amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # إدراج المصروف في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO المصاريف (التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, طريقة_الأداء, ملاحظات)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                self.date_edit.date().toString("yyyy-MM-dd"),
                amount,
                self.expense_type_combo.currentText(),
                self.beneficiary_input.text().strip(),
                self.payment_method_combo.currentText(),
                self.notes_input.text().strip()
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")
            self.clear_form()
            self.load_expenses()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المصروف: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.date_edit.setDate(QDate.currentDate())
        self.amount_input.clear()
        if self.expense_type_combo.count() > 0:
            self.expense_type_combo.setCurrentIndex(0)
        self.beneficiary_input.clear()
        self.payment_method_combo.setCurrentIndex(0)
        self.notes_input.clear()

    def filter_expenses(self):
        """تصفية المصاريف"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()
        month_filter = self.month_filter_combo.currentText()

        for row in range(self.expenses_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.expenses_table.columnCount()):
                    item = self.expenses_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع الأنواع":
                type_item = self.expenses_table.item(row, 3)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            # فلتر الشهر
            if month_filter and month_filter != "جميع الشهور":
                date_item = self.expenses_table.item(row, 1)
                if date_item:
                    try:
                        date_str = date_item.text()
                        if date_str:
                            # تحويل التاريخ لاستخراج الشهر
                            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                            month_names = ["", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                            expense_month = month_names[date_obj.month]
                            if expense_month != month_filter:
                                show_row = False
                    except:
                        pass

            self.expenses_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.expenses_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.expenses_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_expense()
        elif action == delete_action:
            self.delete_expense()

    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات المصروف
        expense_id = self.expenses_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM المصاريف WHERE id = ?", (expense_id,))
            expense = cursor.fetchone()

            if expense:
                # تعبئة النموذج بالبيانات الحالية
                self.date_edit.setDate(QDate.fromString(expense[1], "yyyy-MM-dd"))
                self.amount_input.setText(str(expense[2]))

                # البحث عن نوع المصروف في القائمة
                type_index = self.expense_type_combo.findText(expense[3])
                if type_index >= 0:
                    self.expense_type_combo.setCurrentIndex(type_index)

                self.beneficiary_input.setText(expense[4] or "")

                # البحث عن طريقة الأداء في القائمة
                payment_index = self.payment_method_combo.findText(expense[5])
                if payment_index >= 0:
                    self.payment_method_combo.setCurrentIndex(payment_index)

                self.notes_input.setText(expense[6] or "")

                # حذف المصروف القديم
                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات المصروف للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة المصروف'.")

            conn.close()
            self.load_expenses()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل المصروف: {str(e)}")

    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        expense_id = self.expenses_table.item(current_row, 0).text()
        expense_amount = self.expenses_table.item(current_row, 2).text()
        expense_type = self.expenses_table.item(current_row, 3).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا المصروف؟\n\nالنوع: {expense_type}\nالمبلغ: {expense_amount}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف المصروف بنجاح")
                self.load_expenses()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")

    def generate_expense_receipt(self):
        """إنشاء تقرير سند الصرف PDF"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف من الجدول لإنشاء سند الصرف")
            return

        try:
            # جلب بيانات المصروف المحدد
            expense_id = self.expenses_table.item(current_row, 0).text()
            expense_date = self.expenses_table.item(current_row, 1).text()
            expense_amount = self.expenses_table.item(current_row, 2).text()
            expense_type = self.expenses_table.item(current_row, 3).text()
            expense_beneficiary = self.expenses_table.item(current_row, 4).text()
            expense_method = self.expenses_table.item(current_row, 5).text()
            expense_notes = self.expenses_table.item(current_row, 6).text()

            # استيراد مكتبة PDF
            try:
                from reportlab.lib.pagesizes import A4, portrait
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch, cm
                from reportlab.lib import colors
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
            except ImportError:
                QMessageBox.critical(self, "خطأ", "مكتبة reportlab غير مثبتة\nيرجى تثبيتها باستخدام:\npip install reportlab")
                return

            # تحديد مسار الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ سند الصرف",
                f"سند_صرف_{expense_id}_{expense_date}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # إنشاء المستند
            doc = SimpleDocTemplate(
                file_path,
                pagesize=portrait(A4),
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            # قائمة العناصر
            story = []

            # تسجيل خط عربي (إذا كان متوفراً)
            try:
                pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
                arabic_font = 'Arabic'
            except:
                arabic_font = 'Helvetica'

            # أنماط النص
            styles = getSampleStyleSheet()

            # نمط العنوان الرئيسي
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=arabic_font,
                fontSize=18,
                alignment=TA_CENTER,
                spaceAfter=30,
                textColor=colors.darkblue
            )

            # نمط النص العادي
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontName=arabic_font,
                fontSize=12,
                alignment=TA_RIGHT,
                spaceAfter=12
            )

            # نمط النص المتوسط
            center_style = ParagraphStyle(
                'CustomCenter',
                parent=styles['Normal'],
                fontName=arabic_font,
                fontSize=14,
                alignment=TA_CENTER,
                spaceAfter=20
            )

            # شعار المؤسسة (نص بديل)
            story.append(Paragraph("🏫", title_style))
            story.append(Spacer(1, 0.2*inch))

            # اسم المؤسسة
            story.append(Paragraph("المؤسسة التعليمية", title_style))
            story.append(Spacer(1, 0.3*inch))

            # عنوان التقرير
            story.append(Paragraph("سند الصرف", title_style))
            story.append(Spacer(1, 0.5*inch))

            # بيانات السند
            receipt_data = [
                ["رقم السند:", expense_id],
                ["التاريخ:", expense_date],
                ["المبلغ:", expense_amount + " درهم"],
                ["نوع المصروف:", expense_type],
                ["الجهة المستفيدة:", expense_beneficiary],
                ["طريقة الأداء:", expense_method],
                ["ملاحظات:", expense_notes if expense_notes else "لا توجد"]
            ]

            # إنشاء جدول البيانات
            receipt_table = Table(receipt_data, colWidths=[4*cm, 8*cm])
            receipt_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.lightgrey])
            ]))

            story.append(receipt_table)
            story.append(Spacer(1, 1*inch))

            # التوقيع والتاريخ
            signature_data = [
                ["التوقيع: ________________", f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"]
            ]

            signature_table = Table(signature_data, colWidths=[6*cm, 6*cm])
            signature_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
            ]))

            story.append(signature_table)

            # بناء المستند
            doc.build(story)

            QMessageBox.information(self, "نجح", f"تم إنشاء سند الصرف بنجاح:\n{file_path}")

            # فتح الملف
            import os
            os.startfile(file_path)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء سند الصرف: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = ExpenseManagementWindow()
    window.show()

    sys.exit(app.exec_())
