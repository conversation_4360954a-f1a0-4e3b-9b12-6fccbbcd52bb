# ================= نافذة مسك المصاريف =================
# ملف منفصل لإدارة المصاريف المالية للمؤسسة التعليمية
# يحتوي على: إدخال المصاريف، تصنيفها، البحث والتصفية، التقارير

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ExpenseManager:
    """مدير المصاريف"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول المصاريف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ DATE NOT NULL,
                    المبلغ REAL NOT NULL,
                    نوع_المصروف TEXT NOT NULL,
                    الجهة_المستفيدة TEXT,
                    طريقة_الأداء TEXT,
                    ملاحظات TEXT,
                    تاريخ_الإدخال TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول أنواع المصاريف
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS أنواع_المصاريف (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_النوع TEXT UNIQUE NOT NULL,
                    وصف_النوع TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)
            
            # إدراج أنواع المصاريف الافتراضية
            default_expense_types = [
                ('رواتب', 'رواتب الموظفين والمدرسين'),
                ('كراء', 'إيجار المباني والمرافق'),
                ('فواتير', 'فواتير الكهرباء والماء والهاتف والإنترنت'),
                ('معدات', 'شراء المعدات والأجهزة التعليمية'),
                ('صيانة', 'صيانة المباني والمعدات'),
                ('إعلانات', 'الدعاية والإعلان والتسويق'),
                ('قرطاسية', 'المواد المكتبية والقرطاسية'),
                ('نقل', 'مصاريف النقل والمواصلات'),
                ('تأمين', 'أقساط التأمين المختلفة'),
                ('ضرائب', 'الضرائب والرسوم الحكومية'),
                ('أخرى', 'مصاريف متنوعة أخرى')
            ]
            
            cursor.executemany("""
                INSERT OR IGNORE INTO أنواع_المصاريف (اسم_النوع, وصف_النوع) 
                VALUES (?, ?)
            """, default_expense_types)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات المصاريف: {str(e)}")

class ExpenseManagementWindow(QMainWindow):
    """نافذة مسك المصاريف"""
    
    def __init__(self):
        super().__init__()
        self.manager = ExpenseManager()
        self.init_ui()
        self.load_expense_types()
        self.load_expenses()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💰 مسك المصاريف")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        self.center_window(self)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("💰 نظام مسك المصاريف")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 2px solid #3498db;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        main_layout.addLayout(content_layout)
        
        # الجانب الأيسر - نموذج إدخال المصروف
        self.create_expense_form(content_layout)
        
        # الجانب الأيمن - جدول المصاريف
        self.create_expenses_table(content_layout)
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز لإدخال المصاريف")
        self.statusBar().setFont(QFont("Calibri", 10))
    
    def create_expense_form(self, parent_layout):
        """إنشاء نموذج إدخال المصروف"""
        form_group = QGroupBox("📝 إضافة مصروف جديد")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #27ae60;
            }
        """)
        form_group.setMaximumWidth(450)
        
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        
        # التاريخ
        date_layout = QFormLayout()
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setFont(QFont("Calibri", 12))
        self.date_edit.setStyleSheet(self.get_input_style())
        date_layout.addRow(self.create_label("📅 التاريخ:"), self.date_edit)
        form_layout.addLayout(date_layout)
        
        # المبلغ
        amount_layout = QFormLayout()
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("أدخل المبلغ بالدرهم...")
        self.amount_input.setFont(QFont("Calibri", 12))
        self.amount_input.setStyleSheet(self.get_input_style())
        amount_layout.addRow(self.create_label("💵 المبلغ:"), self.amount_input)
        form_layout.addLayout(amount_layout)
        
        # نوع المصروف
        type_layout = QFormLayout()
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.setFont(QFont("Calibri", 12))
        self.expense_type_combo.setStyleSheet(self.get_combo_style())
        type_layout.addRow(self.create_label("📋 نوع المصروف:"), self.expense_type_combo)
        form_layout.addLayout(type_layout)
        
        # الجهة المستفيدة
        beneficiary_layout = QFormLayout()
        self.beneficiary_input = QLineEdit()
        self.beneficiary_input.setPlaceholderText("اسم الجهة أو المورد...")
        self.beneficiary_input.setFont(QFont("Calibri", 12))
        self.beneficiary_input.setStyleSheet(self.get_input_style())
        beneficiary_layout.addRow(self.create_label("🏢 الجهة المستفيدة:"), self.beneficiary_input)
        form_layout.addLayout(beneficiary_layout)
        
        # طريقة الأداء
        payment_layout = QFormLayout()
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setFont(QFont("Calibri", 12))
        self.payment_method_combo.setStyleSheet(self.get_combo_style())
        payment_layout.addRow(self.create_label("💳 طريقة الأداء:"), self.payment_method_combo)
        form_layout.addLayout(payment_layout)
        
        # الملاحظات
        notes_layout = QFormLayout()
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية...")
        self.notes_input.setFont(QFont("Calibri", 11))
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setStyleSheet(self.get_input_style())
        notes_layout.addRow(self.create_label("📝 ملاحظات:"), self.notes_input)
        form_layout.addLayout(notes_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_btn = QPushButton("➕ إضافة المصروف")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet(self.get_button_style("#27ae60"))
        add_btn.clicked.connect(self.add_expense)
        
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        clear_btn.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(clear_btn)
        form_layout.addLayout(buttons_layout)
        
        parent_layout.addWidget(form_group)

    def create_expenses_table(self, parent_layout):
        """إنشاء جدول المصاريف"""
        table_group = QGroupBox("📊 سجل المصاريف")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #3498db;
            }
        """)

        table_layout = QVBoxLayout(table_group)

        # أدوات التحكم
        controls_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Calibri", 11, QFont.Bold))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في المصاريف...")
        self.search_input.setFont(QFont("Calibri", 11))
        self.search_input.setStyleSheet(self.get_input_style())
        self.search_input.textChanged.connect(self.filter_expenses)

        # فلتر النوع
        type_filter_label = QLabel("📋 النوع:")
        type_filter_label.setFont(QFont("Calibri", 11, QFont.Bold))
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.setFont(QFont("Calibri", 11))
        self.type_filter_combo.setStyleSheet(self.get_combo_style())
        self.type_filter_combo.currentTextChanged.connect(self.filter_expenses)

        controls_layout.addWidget(search_label)
        controls_layout.addWidget(self.search_input)
        controls_layout.addWidget(type_filter_label)
        controls_layout.addWidget(self.type_filter_combo)
        controls_layout.addStretch()

        table_layout.addLayout(controls_layout)

        # الجدول
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(8)
        self.expenses_table.setHorizontalHeaderLabels([
            "ID", "التاريخ", "المبلغ", "نوع المصروف",
            "الجهة المستفيدة", "طريقة الأداء", "ملاحظات", "تاريخ الإدخال"
        ])

        # تنسيق الجدول
        self.expenses_table.setFont(QFont("Calibri", 10))
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.horizontalHeader().setStretchLastSection(True)

        # تنسيق رأس الجدول - برتقالي مثل archived_accounts_window
        header = self.expenses_table.horizontalHeader()
        header.setFont(QFont("Calibri", 11, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # قائمة السياق
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.expenses_table)

        parent_layout.addWidget(table_group)

    def create_label(self, text):
        """إنشاء تسمية منسقة"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 11, QFont.Bold))
        label.setStyleSheet("color: #2c3e50; margin: 5px;")
        return label

    def get_input_style(self):
        """تنسيق حقول الإدخال"""
        return """
            QLineEdit, QTextEdit, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus, QDateEdit:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_combo_style(self):
        """تنسيق القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 12px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
            }
        """

    def get_button_style(self, color):
        """تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, hex_color, factor=0.9):
        """تغميق اللون"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def load_expense_types(self):
        """تحميل أنواع المصاريف"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT اسم_النوع FROM أنواع_المصاريف WHERE نشط = 1 ORDER BY اسم_النوع")
            types = cursor.fetchall()

            self.expense_type_combo.clear()
            self.type_filter_combo.clear()
            self.type_filter_combo.addItem("جميع الأنواع")

            for type_row in types:
                type_name = type_row[0]
                self.expense_type_combo.addItem(type_name)
                self.type_filter_combo.addItem(type_name)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل أنواع المصاريف: {str(e)}")

    def load_expenses(self):
        """تحميل المصاريف في الجدول"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة,
                       طريقة_الأداء, ملاحظات, تاريخ_الإدخال
                FROM المصاريف
                ORDER BY التاريخ DESC, تاريخ_الإدخال DESC
            """)
            expenses = cursor.fetchall()

            self.expenses_table.setRowCount(len(expenses))

            for row, expense in enumerate(expenses):
                for col, value in enumerate(expense):
                    if col == 2:  # المبلغ
                        item = QTableWidgetItem(f"{float(value):,.2f} درهم")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")

                    item.setFont(QFont("Calibri", 10))
                    self.expenses_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(expenses)} مصروف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المصاريف: {str(e)}")

    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ")
                return

            if not self.expense_type_combo.currentText():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع المصروف")
                return

            try:
                amount = float(self.amount_input.text().strip())
                if amount <= 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # إدراج المصروف في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO المصاريف (التاريخ, المبلغ, نوع_المصروف, الجهة_المستفيدة, طريقة_الأداء, ملاحظات)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                self.date_edit.date().toString("yyyy-MM-dd"),
                amount,
                self.expense_type_combo.currentText(),
                self.beneficiary_input.text().strip(),
                self.payment_method_combo.currentText(),
                self.notes_input.toPlainText().strip()
            ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح")
            self.clear_form()
            self.load_expenses()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المصروف: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.date_edit.setDate(QDate.currentDate())
        self.amount_input.clear()
        self.expense_type_combo.setCurrentIndex(0)
        self.beneficiary_input.clear()
        self.payment_method_combo.setCurrentIndex(0)
        self.notes_input.clear()

    def filter_expenses(self):
        """تصفية المصاريف"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()

        for row in range(self.expenses_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.expenses_table.columnCount()):
                    item = self.expenses_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع الأنواع":
                type_item = self.expenses_table.item(row, 3)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            self.expenses_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.expenses_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.expenses_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_expense()
        elif action == delete_action:
            self.delete_expense()

    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات المصروف
        expense_id = self.expenses_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM المصاريف WHERE id = ?", (expense_id,))
            expense = cursor.fetchone()

            if expense:
                # تعبئة النموذج بالبيانات الحالية
                self.date_edit.setDate(QDate.fromString(expense[1], "yyyy-MM-dd"))
                self.amount_input.setText(str(expense[2]))

                # البحث عن نوع المصروف في القائمة
                type_index = self.expense_type_combo.findText(expense[3])
                if type_index >= 0:
                    self.expense_type_combo.setCurrentIndex(type_index)

                self.beneficiary_input.setText(expense[4] or "")

                # البحث عن طريقة الأداء في القائمة
                payment_index = self.payment_method_combo.findText(expense[5])
                if payment_index >= 0:
                    self.payment_method_combo.setCurrentIndex(payment_index)

                self.notes_input.setPlainText(expense[6] or "")

                # حذف المصروف القديم
                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات المصروف للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة المصروف'.")

            conn.close()
            self.load_expenses()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل المصروف: {str(e)}")

    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            return

        expense_id = self.expenses_table.item(current_row, 0).text()
        expense_amount = self.expenses_table.item(current_row, 2).text()
        expense_type = self.expenses_table.item(current_row, 3).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا المصروف؟\n\nالنوع: {expense_type}\nالمبلغ: {expense_amount}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM المصاريف WHERE id = ?", (expense_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف المصروف بنجاح")
                self.load_expenses()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = ExpenseManagementWindow()
    window.show()

    sys.exit(app.exec_())
