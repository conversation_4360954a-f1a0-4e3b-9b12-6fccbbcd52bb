#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ========================================
# 🔧 إعدادات التقرير اليومي القابلة للتعديل
# ========================================

# 📏 عرض الأعمدة بالمليمتر (غير القيم حسب الحاجة)
COLUMN_WIDTHS = {
    'name': 61,       # اسم التلميذ
    'id': 40,         # الرمز
    'order': 21,      # رقم الترتيب
    'session': 15.6     # كل حصة يومية
}

# 📐 ارتفاع الصفوف بالمليمتر (غير القيم حسب الحاجة)
ROW_HEIGHTS = {
    'header_main': 12,      # رأس الجدول الرئيسي
    'header_sub': 6,        # رأس الجدول الفرعي (الحصص)
    'student_row': 4.5,       # صف التلميذ
    'info_row': 6          # صف معلومات القسم
}

# 📊 عرض أعمدة الجدول الأول (معلومات القسم) بالمليمتر
INFO_TABLE_WIDTHS = {
    'section': 48,         # عرض عمود القسم
    'subject': 48,         # عرض عمود المادة
    'teacher': 64,         # عرض عمود الأستاذ
    'count': 40           # عرض عمود عدد التلاميذ
}

# 🖼️ نمط الحدود (غير القيم حسب الحاجة)
BORDER_STYLE = {
    'width': 0.3,           # سمك الحدود
    'color_r': 0,           # اللون الأحمر (0-255)
    'color_g': 0,           # اللون الأخضر (0-255)
    'color_b': 0,           # اللون الأزرق (0-255)
    'style': 'solid'        # نمط الخط: solid, dashed, dotted
}

# 📄 إعدادات الصفحة (غير القيم حسب الحاجة)
PAGE_SETTINGS = {
    'orientation': 'P',     # P = عمودي، L = أفقي
    'margin_top': 5,        # الهامش العلوي
    'margin_bottom': 5,     # الهامش السفلي
    'margin_left': 5,       # الهامش الأيسر
    'margin_right': 5       # الهامش الأيمن
}

# ========================================

import os
import sys
import sqlite3
import subprocess
from datetime import datetime

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__(PAGE_SETTINGS['orientation'], 'mm', 'A4')
        self.set_margins(
            PAGE_SETTINGS['margin_left'],
            PAGE_SETTINGS['margin_top'],
            PAGE_SETTINGS['margin_right']
        )
        self.set_auto_page_break(auto=True, margin=PAGE_SETTINGS['margin_bottom'])
        
        # إضافة الخطوط
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي والحدود
        if self.calibri_available:
            self.set_font('Calibri', '', 10)
        else:
            self.set_font('Arial', '', 10)

        # تعيين نمط الحدود من الإعدادات
        self.set_draw_color(
            BORDER_STYLE['color_r'],
            BORDER_STYLE['color_g'],
            BORDER_STYLE['color_b']
        )
        self.set_line_width(BORDER_STYLE['width'])

    def set_main_title_font(self):
        """خط العناوين الرئيسية - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 15)
        else:
            self.set_font('Arial', 'B', 15)

    def set_subtitle_font(self):
        """خط العناوين الفرعية - Calibri 14 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 14)
        else:
            self.set_font('Arial', 'B', 14)

    def set_detail_font(self):
        """خط التفاصيل - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)

    def set_table_header_font(self):
        """خط رؤوس الجدول - Calibri 15 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 12)
        else:
            self.set_font('Arial', 'B', 12)

    def set_table_row_font(self):
        """خط صفوف الجدول - Calibri 13 Bold"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', 10)
        else:
            self.set_font('Arial', 'B', 10)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def add_page_footer(pdf, page_width, y):
    """إضافة المعلومات والتوقيع في نهاية الصفحة"""
    # المعلومات
    y += 5
    pdf.set_detail_font()

    notes = [
        "• يتم وضع علامة (×) في الخانة المقابلة للحصة التي غاب فيها التلميذ"
    ]

    for note in notes:
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 5, pdf.ar_text(note), border=0, align='R')
        y += 6

    # التوقيع والتاريخ
    y += 2
    pdf.set_xy(0.2, y)
    pdf.cell(page_width / 2, 8, pdf.ar_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"), border=0, align='R')

    pdf.set_xy(0.2 + page_width / 2, y)
    pdf.cell(page_width / 2, 8, pdf.ar_text("التوقيع: ________________"), border=0, align='R')

def generate_daily_attendance_sheet_pdf(section, selected_date, num_sessions, db_path="data.db"):
    """إنشاء ورقة متابعة الغياب اليومية PDF"""
    try:
        print(f"🔧 بدء إنشاء ورقة PDF اليومية للقسم: {section}")

        # تحديد عرض الأعمدة حسب عدد الحصص
        if num_sessions == 6:
            session_width = 13
        elif num_sessions == 5:
            session_width = 15.6
        elif num_sessions == 4:
            session_width = 19.5
        elif num_sessions == 3:
            session_width = 26
        else:
            session_width = 12  # افتراضي

        # تحديث عرض الأعمدة
        column_widths = {
            'name': 61,
            'id': 40,
            'order': 21,
            'session': session_width
        }

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب معلومات القسم
        try:
            cursor.execute("""
                SELECT القسم, المادة, اسم_الاستاذ
                FROM جدول_المواد_والاقسام
                WHERE القسم = ?
                LIMIT 1
            """, (section,))

            section_info = cursor.fetchone()
            if not section_info:
                section_info = (section, "غير محدد", "غير محدد")
        except Exception as e:
            print(f"⚠️ خطأ في جلب معلومات القسم: {e}")
            section_info = (section, "غير محدد", "غير محدد")
        
        # جلب التلاميذ مرتبين حسب الرمز
        cursor.execute("""
            SELECT id, اسم_التلميذ, رمز_التلميذ
            FROM جدول_البيانات 
            WHERE القسم = ? AND اسم_التلميذ IS NOT NULL AND اسم_التلميذ != ''
            ORDER BY CAST(رمز_التلميذ AS INTEGER) ASC, اسم_التلميذ ASC
        """, (section,))
        
        students = cursor.fetchall()
        conn.close()
        
        if not students:
            raise Exception(f"لا يوجد تلاميذ في القسم '{section}'")
        
        # إنشاء PDF
        pdf = ArabicPDF()
        pdf.add_page()
        
        # الشعار والعنوان
        y = 1  # بداية أعلى بسبب الهوامش الصغيرة
        page_width = 210 - 0.4  # عرض الصفحة مطروح منه الهوامش

        # شعار المؤسسة (إذا كان متوفراً)
        logo_path = None
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            if logo_row and os.path.exists(logo_row[0]):
                logo_path = logo_row[0]
            conn.close()
        except:
            pass

        if logo_path:
            pdf.image(logo_path, x=(page_width - 40) / 2 + 0.2, y=y, w=40, h=20)
            y += 20
        else:
            # شعار نصي
            pdf.set_main_title_font()
            pdf.set_xy(0.2, y)
            pdf.cell(page_width, 10, pdf.ar_text("🏫 مؤسسة التعليم"), border=0, align='C')
            y += 8

        # عنوان الورقة
        pdf.set_main_title_font()
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 10, pdf.ar_text("ورقة الغياب اليومية"), border=0, align='C')
        y += 8

        pdf.set_subtitle_font()
        pdf.set_xy(0.2, y)
        pdf.cell(page_width, 8, pdf.ar_text(f"تاريخ {selected_date.strftime('%Y-%m-%d')}"), border=0, align='C')
        y += 8

        # معلومات القسم
        pdf.set_detail_font()
        info_y = y

        # استخدام العروض القابلة للتعديل
        x_pos = PAGE_SETTINGS['margin_left']

        # الصف الأول من المعلومات
        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['section'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"القسم: {section_info[0]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['section']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['subject'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"المادة: {section_info[1]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['subject']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['teacher'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"الأستاذ(ة): {section_info[2]}"), border=1, align='C')
        x_pos += INFO_TABLE_WIDTHS['teacher']

        pdf.set_xy(x_pos, info_y)
        pdf.cell(INFO_TABLE_WIDTHS['count'], ROW_HEIGHTS['info_row'], pdf.ar_text(f"عدد التلاميذ: {len(students)}"), border=1, align='C')

        y += ROW_HEIGHTS['info_row'] + 1

        # جدول الغياب اليومي
        pdf.set_table_header_font()

        # حساب عرض الجدول
        sessions_width = column_widths['session'] * num_sessions
        total_width = column_widths['order'] + column_widths['id'] + column_widths['name'] + sessions_width

        # رأس الجدول - الصف الأول (الأعمدة الرئيسية)
        x = PAGE_SETTINGS['margin_left']

        # عمود الرقم الترتيبي
        pdf.rect(x, y, column_widths['order'], ROW_HEIGHTS['header_main'])
        pdf.set_xy(x, y + 2)
        pdf.cell(column_widths['order'], ROW_HEIGHTS['header_main']/2 - 2, pdf.ar_text("الرقم"), border=0, align='C')
        pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/2 + 1)
        pdf.cell(column_widths['order'], ROW_HEIGHTS['header_main']/2 - 2, pdf.ar_text("الترتيبي"), border=0, align='C')
        x += column_widths['order']

        # عمود الرمز
        pdf.rect(x, y, column_widths['id'], ROW_HEIGHTS['header_main'])
        pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/4)
        pdf.cell(column_widths['id'], ROW_HEIGHTS['header_main']/2, pdf.ar_text("الرمز"), border=0, align='C')
        x += column_widths['id']

        # عمود اسم التلميذ
        pdf.rect(x, y, column_widths['name'], ROW_HEIGHTS['header_main'])
        pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/4)
        pdf.cell(column_widths['name'], ROW_HEIGHTS['header_main']/2, pdf.ar_text("اسم التلميذ"), border=0, align='C')
        x += column_widths['name']

        # عمود الحصص
        pdf.rect(x, y, sessions_width, ROW_HEIGHTS['header_main'])
        pdf.set_xy(x, y + ROW_HEIGHTS['header_main']/4)
        pdf.cell(sessions_width, ROW_HEIGHTS['header_main']/2, pdf.ar_text("الحصص"), border=0, align='C')

        # رأس الجدول - الصف الثاني (أرقام الحصص)
        y += ROW_HEIGHTS['header_main']
        x = PAGE_SETTINGS['margin_left']

        # خلايا فارغة للأعمدة الثابتة
        pdf.set_xy(x, y)
        pdf.cell(column_widths['order'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')
        x += column_widths['order']

        pdf.set_xy(x, y)
        pdf.cell(column_widths['id'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')
        x += column_widths['id']

        pdf.set_xy(x, y)
        pdf.cell(column_widths['name'], ROW_HEIGHTS['header_sub'], "", border=1, align='C')
        x += column_widths['name']

        # أرقام الحصص (معكوسة - من الأخيرة إلى الأولى)
        for session_num in range(num_sessions, 0, -1):
            pdf.set_xy(x, y)
            pdf.cell(column_widths['session'], ROW_HEIGHTS['header_sub'], pdf.ar_text(f"ح{session_num}"), border=1, align='C')
            x += column_widths['session']

        y += ROW_HEIGHTS['header_sub']

        # صفوف التلاميذ
        pdf.set_table_row_font()
        for i, student in enumerate(students, 1):
            student_id, student_name, student_code = student

            x = PAGE_SETTINGS['margin_left']

            # الرقم الترتيبي
            pdf.set_xy(x, y)
            pdf.cell(column_widths['order'], ROW_HEIGHTS['student_row'], str(i), border=1, align='C')
            x += column_widths['order']

            # الرمز
            pdf.set_xy(x, y)
            pdf.cell(column_widths['id'], ROW_HEIGHTS['student_row'], str(student_code or student_id), border=1, align='C')
            x += column_widths['id']

            # اسم التلميذ
            pdf.set_xy(x, y)
            pdf.cell(column_widths['name'], ROW_HEIGHTS['student_row'], pdf.ar_text(str(student_name)), border=1, align='R')
            x += column_widths['name']

            # خلايا الحصص (معكوسة - فارغة للتعبئة اليدوية)
            # نبدأ من الحصة الأخيرة إلى الأولى لتتطابق مع الرؤوس
            for session_num in range(num_sessions, 0, -1):
                pdf.set_xy(x, y)
                pdf.cell(column_widths['session'], ROW_HEIGHTS['student_row'], "", border=1, align='C')
                x += column_widths['session']

            y += ROW_HEIGHTS['student_row']

            # انتقال لصفحة جديدة عند الحاجة
            if y > 250:
                add_page_footer(pdf, page_width, y)
                pdf.add_page()
                y = 20

        # إضافة المعلومات والتوقيع في نهاية الصفحة الأخيرة
        add_page_footer(pdf, page_width, y)

        return pdf

    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF اليومي: {e}")
        raise e

def create_daily_attendance_sheet_report(section, selected_date, num_sessions, db_path="data.db"):
    """إنشاء تقرير ورقة الغياب اليومية وحفظه"""
    try:
        print(f"📋 بدء إنشاء تقرير ورقة الغياب اليومية...")
        print(f"📅 القسم: {section}")
        print(f"📅 التاريخ: {selected_date}")
        print(f"🕐 عدد الحصص: {num_sessions}")

        # إنشاء PDF
        pdf = generate_daily_attendance_sheet_pdf(section, selected_date, num_sessions, db_path)

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
            print(f"📁 تم إنشاء مجلد التقارير: {reports_dir}")

        # تحديد اسم الملف (إزالة الأحرف الخاصة)
        date_str = selected_date.strftime('%Y-%m-%d')
        # تنظيف اسم القسم من الأحرف الخاصة
        clean_section = section.replace("/", "_").replace("\\", "_").replace(" ", "_").replace(":", "_")
        filename = f"ورقة_الغياب_اليومية_{clean_section}_{date_str}_{num_sessions}حصص.pdf"
        output_path = os.path.join(reports_dir, filename)

        # حفظ الملف
        pdf.output(output_path)
        print(f"✅ تم حفظ التقرير: {output_path}")

        # فتح الملف تلقائياً
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                subprocess.Popen(['start', '', output_path], shell=True)
                print(f"📖 تم فتح التقرير تلقائياً")
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', output_path])
                print(f"📖 تم فتح التقرير تلقائياً")
        except Exception as e:
            print(f"⚠️ تعذر فتح التقرير تلقائياً: {e}")
            print(f"📁 يمكنك العثور على التقرير في: {output_path}")

        return True, output_path, "تم إنشاء ورقة الغياب اليومية بنجاح"

    except Exception as e:
        error_msg = f"فشل في إنشاء ورقة الغياب اليومية: {str(e)}"
        print(f"❌ {error_msg}")
        return False, None, error_msg

if __name__ == "__main__":
    # اختبار سريع
    from datetime import date
    test_date = date.today()
    success, path, message = create_daily_attendance_sheet_report("القسم الأول", test_date, 4)
    print(f"النتيجة: {success}")
    print(f"المسار: {path}")
    print(f"الرسالة: {message}")
