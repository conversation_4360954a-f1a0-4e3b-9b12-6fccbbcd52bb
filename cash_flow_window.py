# ================= نافذة التدفقات النقدية (Cash Flow) =================
# ملف منفصل لإدارة التدفقات النقدية للمؤسسة التعليمية
# يحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# تعيين الخط العربي لـ matplotlib
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

class CashFlowManager:
    """مدير التدفقات النقدية"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    
    def get_cash_inflows(self, start_date, end_date):
        """جلب التدفقات النقدية الداخلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            inflows = []
            
            # التدفقات من رسوم التسجيل
            cursor.execute("""
                SELECT payment_date, amount_paid, 'رسوم تسجيل' as source, 
                       payment_method, notes
                FROM registration_fees 
                WHERE payment_date BETWEEN ? AND ?
                ORDER BY payment_date
            """, (start_date, end_date))
            
            for row in cursor.fetchall():
                inflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'source': row[2],
                    'method': row[3],
                    'notes': row[4] or ''
                })
            
            # التدفقات من الواجبات الشهرية
            cursor.execute("""
                SELECT payment_date, amount_paid, 'واجبات شهرية' as source,
                       'نقدي' as method, notes
                FROM monthly_duties 
                WHERE payment_date BETWEEN ? AND ?
                AND payment_status = 'مدفوع كاملاً'
                ORDER BY payment_date
            """, (start_date, end_date))
            
            for row in cursor.fetchall():
                inflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'source': row[2],
                    'method': row[3],
                    'notes': row[4] or ''
                })
            
            conn.close()
            return sorted(inflows, key=lambda x: x['date'])
            
        except Exception as e:
            print(f"خطأ في جلب التدفقات الداخلة: {str(e)}")
            return []
    
    def get_cash_outflows(self, start_date, end_date):
        """جلب التدفقات النقدية الخارجة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT التاريخ, المبلغ, نوع_المصروف, طريقة_الأداء, 
                       الجهة_المستفيدة, ملاحظات
                FROM المصاريف 
                WHERE التاريخ BETWEEN ? AND ?
                ORDER BY التاريخ
            """, (start_date, end_date))
            
            outflows = []
            for row in cursor.fetchall():
                outflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'type': row[2],
                    'method': row[3],
                    'beneficiary': row[4] or '',
                    'notes': row[5] or ''
                })
            
            conn.close()
            return outflows
            
        except Exception as e:
            print(f"خطأ في جلب التدفقات الخارجة: {str(e)}")
            return []

class CashFlowWindow(QMainWindow):
    """نافذة التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.setGeometry(100, 100, 1600, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        self.center_window(self)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 2px solid #16a085;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # شريط أدوات التحكم
        self.create_controls_bar(main_layout)
        
        # تبويبات التدفقات النقدية
        tabs = QTabWidget()
        tabs.setFont(QFont("Calibri", 11, QFont.Bold))
        
        # تبويب ملخص التدفقات
        self.create_summary_tab(tabs)
        
        # تبويب التدفقات الداخلة
        self.create_inflows_tab(tabs)
        
        # تبويب التدفقات الخارجة
        self.create_outflows_tab(tabs)
        
        # تبويب الرسوم البيانية
        self.create_charts_tab(tabs)
        
        main_layout.addWidget(tabs)
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز لعرض التدفقات النقدية")
        self.statusBar().setFont(QFont("Calibri", 10))
    
    def create_controls_bar(self, parent_layout):
        """إنشاء شريط أدوات التحكم"""
        controls_group = QGroupBox("🎛️ فترة التقرير")
        controls_group.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #16a085;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #16a085;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_group)
        
        # تاريخ البداية
        start_label = QLabel("📅 من تاريخ:")
        start_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 11))
        self.start_date.setStyleSheet(self.get_input_style())
        
        # تاريخ النهاية
        end_label = QLabel("📅 إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 11))
        self.end_date.setStyleSheet(self.get_input_style())
        
        # أزرار سريعة
        today_btn = QPushButton("📅 اليوم")
        today_btn.setFont(QFont("Calibri", 10, QFont.Bold))
        today_btn.setStyleSheet(self.get_button_style("#3498db"))
        today_btn.clicked.connect(self.set_today)
        
        week_btn = QPushButton("📅 هذا الأسبوع")
        week_btn.setFont(QFont("Calibri", 10, QFont.Bold))
        week_btn.setStyleSheet(self.get_button_style("#27ae60"))
        week_btn.clicked.connect(self.set_this_week)
        
        month_btn = QPushButton("📅 هذا الشهر")
        month_btn.setFont(QFont("Calibri", 10, QFont.Bold))
        month_btn.setStyleSheet(self.get_button_style("#e67e22"))
        month_btn.clicked.connect(self.set_this_month)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        refresh_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        print_btn.setStyleSheet(self.get_button_style("#34495e"))
        print_btn.clicked.connect(self.print_report)
        
        # إضافة العناصر للتخطيط
        controls_layout.addWidget(start_label)
        controls_layout.addWidget(self.start_date)
        controls_layout.addWidget(end_label)
        controls_layout.addWidget(self.end_date)
        controls_layout.addWidget(today_btn)
        controls_layout.addWidget(week_btn)
        controls_layout.addWidget(month_btn)
        controls_layout.addWidget(refresh_btn)
        controls_layout.addWidget(print_btn)
        controls_layout.addStretch()
        
        parent_layout.addWidget(controls_group)
    
    def create_summary_tab(self, tabs):
        """إنشاء تبويب ملخص التدفقات"""
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        
        # منطقة عرض الملخص
        self.summary_text = QTextEdit()
        self.summary_text.setFont(QFont("Calibri", 11))
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        summary_layout.addWidget(self.summary_text)
        
        tabs.addTab(summary_widget, "📊 ملخص التدفقات")
    
    def create_inflows_tab(self, tabs):
        """إنشاء تبويب التدفقات الداخلة"""
        inflows_widget = QWidget()
        inflows_layout = QVBoxLayout(inflows_widget)
        
        # جدول التدفقات الداخلة
        self.inflows_table = QTableWidget()
        self.inflows_table.setColumnCount(5)
        self.inflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "المصدر", "طريقة الدفع", "ملاحظات"
        ])
        
        # تنسيق الجدول
        self.inflows_table.setFont(QFont("Calibri", 10))
        self.inflows_table.setAlternatingRowColors(True)
        self.inflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.inflows_table.horizontalHeader().setStretchLastSection(True)
        
        # تنسيق رأس الجدول - أخضر للتدفقات الداخلة
        header = self.inflows_table.horizontalHeader()
        header.setFont(QFont("Calibri", 11, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #27ae60;
                color: white;
                padding: 8px;
                border: 1px solid #229954;
                font-weight: bold;
            }
        """)
        
        inflows_layout.addWidget(self.inflows_table)
        
        tabs.addTab(inflows_widget, "💰 التدفقات الداخلة")
    
    def create_outflows_tab(self, tabs):
        """إنشاء تبويب التدفقات الخارجة"""
        outflows_widget = QWidget()
        outflows_layout = QVBoxLayout(outflows_widget)
        
        # جدول التدفقات الخارجة
        self.outflows_table = QTableWidget()
        self.outflows_table.setColumnCount(6)
        self.outflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "نوع المصروف", "طريقة الدفع", "الجهة المستفيدة", "ملاحظات"
        ])
        
        # تنسيق الجدول
        self.outflows_table.setFont(QFont("Calibri", 10))
        self.outflows_table.setAlternatingRowColors(True)
        self.outflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.outflows_table.horizontalHeader().setStretchLastSection(True)
        
        # تنسيق رأس الجدول - أحمر للتدفقات الخارجة
        header = self.outflows_table.horizontalHeader()
        header.setFont(QFont("Calibri", 11, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 8px;
                border: 1px solid #c0392b;
                font-weight: bold;
            }
        """)
        
        outflows_layout.addWidget(self.outflows_table)
        
        tabs.addTab(outflows_widget, "💸 التدفقات الخارجة")
    
    def create_charts_tab(self, tabs):
        """إنشاء تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)
        
        # أزرار التحكم في الرسوم البيانية
        chart_controls = QHBoxLayout()
        
        daily_chart_btn = QPushButton("📈 التدفق اليومي")
        daily_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        daily_chart_btn.setStyleSheet(self.get_button_style("#3498db"))
        daily_chart_btn.clicked.connect(self.show_daily_flow_chart)
        
        cumulative_chart_btn = QPushButton("📊 التدفق التراكمي")
        cumulative_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        cumulative_chart_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        cumulative_chart_btn.clicked.connect(self.show_cumulative_chart)
        
        comparison_chart_btn = QPushButton("⚖️ مقارنة التدفقات")
        comparison_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        comparison_chart_btn.setStyleSheet(self.get_button_style("#f39c12"))
        comparison_chart_btn.clicked.connect(self.show_comparison_chart)
        
        chart_controls.addWidget(daily_chart_btn)
        chart_controls.addWidget(cumulative_chart_btn)
        chart_controls.addWidget(comparison_chart_btn)
        chart_controls.addStretch()
        
        charts_layout.addLayout(chart_controls)
        
        # منطقة عرض الرسم البياني
        self.chart_widget = QWidget()
        self.chart_widget.setMinimumHeight(500)
        self.chart_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        charts_layout.addWidget(self.chart_widget)
        
        tabs.addTab(charts_widget, "📈 الرسوم البيانية")

    def get_input_style(self):
        """تنسيق حقول الإدخال"""
        return """
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 11px;
            }
            QDateEdit:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_button_style(self, color):
        """تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, hex_color, factor=0.9):
        """تغميق اللون"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = QDate.currentDate()
        self.start_date.setDate(today)
        self.end_date.setDate(today)

    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = QDate.currentDate()
        start_of_week = today.addDays(-today.dayOfWeek() + 1)
        self.start_date.setDate(start_of_week)
        self.end_date.setDate(today)

    def set_this_month(self):
        """تعيين هذا الشهر"""
        today = QDate.currentDate()
        start_of_month = QDate(today.year(), today.month(), 1)
        self.start_date.setDate(start_of_month)
        self.end_date.setDate(today)

    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            # جلب البيانات
            inflows = self.manager.get_cash_inflows(start_date, end_date)
            outflows = self.manager.get_cash_outflows(start_date, end_date)

            # تحديث الجداول
            self.update_inflows_table(inflows)
            self.update_outflows_table(outflows)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary(inflows, outflows, start_date, end_date)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_inflows_table(self, inflows):
        """تحديث جدول التدفقات الداخلة"""
        self.inflows_table.setRowCount(len(inflows))

        for row, inflow in enumerate(inflows):
            items = [
                inflow['date'],
                f"{inflow['amount']:,.2f} درهم",
                inflow['source'],
                inflow['method'],
                inflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 10))

                # تلوين المبلغ بالأخضر
                if col == 1:
                    item.setForeground(QColor("#27ae60"))
                    item.setFont(QFont("Calibri", 10, QFont.Bold))

                self.inflows_table.setItem(row, col, item)

    def update_outflows_table(self, outflows):
        """تحديث جدول التدفقات الخارجة"""
        self.outflows_table.setRowCount(len(outflows))

        for row, outflow in enumerate(outflows):
            items = [
                outflow['date'],
                f"{outflow['amount']:,.2f} درهم",
                outflow['type'],
                outflow['method'],
                outflow['beneficiary'],
                outflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 10))

                # تلوين المبلغ بالأحمر
                if col == 1:
                    item.setForeground(QColor("#e74c3c"))
                    item.setFont(QFont("Calibri", 10, QFont.Bold))

                self.outflows_table.setItem(row, col, item)

    def generate_cash_flow_summary(self, inflows, outflows, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية"""
        try:
            # حساب الإجماليات
            total_inflows = sum(inflow['amount'] for inflow in inflows)
            total_outflows = sum(outflow['amount'] for outflow in outflows)
            net_cash_flow = total_inflows - total_outflows

            # بناء التقرير
            summary = f"💸 تقرير التدفقات النقدية (Cash Flow)\n"
            summary += f"الفترة: من {start_date} إلى {end_date}\n"
            summary += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            summary += "=" * 60 + "\n\n"

            # ملخص التدفقات الداخلة
            summary += "💰 التدفقات الداخلة:\n"
            summary += "-" * 30 + "\n"

            # تجميع التدفقات الداخلة حسب المصدر
            inflow_sources = {}
            for inflow in inflows:
                source = inflow['source']
                if source not in inflow_sources:
                    inflow_sources[source] = 0
                inflow_sources[source] += inflow['amount']

            for source, amount in inflow_sources.items():
                summary += f"   {source}: {amount:,.2f} درهم\n"

            summary += "-" * 30 + "\n"
            summary += f"   إجمالي التدفقات الداخلة: {total_inflows:,.2f} درهم\n\n"

            # ملخص التدفقات الخارجة
            summary += "💸 التدفقات الخارجة:\n"
            summary += "-" * 30 + "\n"

            # تجميع التدفقات الخارجة حسب النوع
            outflow_types = {}
            for outflow in outflows:
                expense_type = outflow['type']
                if expense_type not in outflow_types:
                    outflow_types[expense_type] = 0
                outflow_types[expense_type] += outflow['amount']

            for expense_type, amount in outflow_types.items():
                summary += f"   {expense_type}: {amount:,.2f} درهم\n"

            summary += "-" * 30 + "\n"
            summary += f"   إجمالي التدفقات الخارجة: {total_outflows:,.2f} درهم\n\n"

            # صافي التدفق النقدي
            summary += "📊 صافي التدفق النقدي:\n"
            summary += "=" * 30 + "\n"

            if net_cash_flow > 0:
                summary += f"   صافي التدفق الإيجابي: {net_cash_flow:,.2f} درهم ✅\n"
                summary += f"   الحالة: تدفق نقدي إيجابي (سيولة جيدة)\n"
            elif net_cash_flow < 0:
                summary += f"   صافي التدفق السلبي: {abs(net_cash_flow):,.2f} درهم ❌\n"
                summary += f"   الحالة: تدفق نقدي سلبي (نقص في السيولة)\n"
            else:
                summary += f"   صافي التدفق: {net_cash_flow:,.2f} درهم ⚖️\n"
                summary += f"   الحالة: متوازن\n"

            # إحصائيات إضافية
            summary += "\n📈 إحصائيات إضافية:\n"
            summary += "-" * 30 + "\n"
            summary += f"   عدد المعاملات الداخلة: {len(inflows)}\n"
            summary += f"   عدد المعاملات الخارجة: {len(outflows)}\n"
            summary += f"   إجمالي المعاملات: {len(inflows) + len(outflows)}\n"

            if len(inflows) > 0:
                avg_inflow = total_inflows / len(inflows)
                summary += f"   متوسط المعاملة الداخلة: {avg_inflow:,.2f} درهم\n"

            if len(outflows) > 0:
                avg_outflow = total_outflows / len(outflows)
                summary += f"   متوسط المعاملة الخارجة: {avg_outflow:,.2f} درهم\n"

            # نسب مالية
            if total_inflows > 0:
                outflow_ratio = (total_outflows / total_inflows) * 100
                summary += f"   نسبة التدفقات الخارجة إلى الداخلة: {outflow_ratio:.2f}%\n"

            self.summary_text.setPlainText(summary)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ملخص التدفقات: {str(e)}")

    def show_daily_flow_chart(self):
        """عرض رسم بياني للتدفق اليومي"""
        QMessageBox.information(self, "قريباً", "رسم التدفق اليومي سيكون متاحاً قريباً")

    def show_cumulative_chart(self):
        """عرض رسم بياني للتدفق التراكمي"""
        QMessageBox.information(self, "قريباً", "رسم التدفق التراكمي سيكون متاحاً قريباً")

    def show_comparison_chart(self):
        """عرض رسم بياني لمقارنة التدفقات"""
        QMessageBox.information(self, "قريباً", "رسم مقارنة التدفقات سيكون متاحاً قريباً")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "طباعة التقرير ستكون متاحة قريباً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())
