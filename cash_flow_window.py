# ================= نافذة التدفقات النقدية (Cash Flow) =================
# ملف منفصل لإدارة التدفقات النقدية للمؤسسة التعليمية
# يحتوي على: تتبع الأموال الداخلة والخارجة، التحليل الزمني، التوقعات

import sys
import os
import sqlite3
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. الرسوم البيانية ستكون معطلة.")

class CashFlowManager:
    """مدير التدفقات النقدية"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    
    def get_cash_inflows(self, start_date, end_date):
        """جلب التدفقات النقدية الداخلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            inflows = []
            
            # التدفقات من رسوم التسجيل
            cursor.execute("""
                SELECT payment_date, amount_paid, 'رسوم تسجيل' as source, 
                       payment_method, notes
                FROM registration_fees 
                WHERE payment_date BETWEEN ? AND ?
                ORDER BY payment_date
            """, (start_date, end_date))
            
            for row in cursor.fetchall():
                inflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'source': row[2],
                    'method': row[3],
                    'notes': row[4] or ''
                })
            
            # التدفقات من الواجبات الشهرية
            cursor.execute("""
                SELECT payment_date, amount_paid, 'واجبات شهرية' as source,
                       'نقدي' as method, notes
                FROM monthly_duties 
                WHERE payment_date BETWEEN ? AND ?
                AND payment_status = 'مدفوع كاملاً'
                ORDER BY payment_date
            """, (start_date, end_date))
            
            for row in cursor.fetchall():
                inflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'source': row[2],
                    'method': row[3],
                    'notes': row[4] or ''
                })
            
            conn.close()
            return sorted(inflows, key=lambda x: x['date'])
            
        except Exception as e:
            print(f"خطأ في جلب التدفقات الداخلة: {str(e)}")
            return []
    
    def get_cash_outflows(self, start_date, end_date):
        """جلب التدفقات النقدية الخارجة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT التاريخ, المبلغ, نوع_المصروف, طريقة_الأداء, 
                       الجهة_المستفيدة, ملاحظات
                FROM المصاريف 
                WHERE التاريخ BETWEEN ? AND ?
                ORDER BY التاريخ
            """, (start_date, end_date))
            
            outflows = []
            for row in cursor.fetchall():
                outflows.append({
                    'date': row[0],
                    'amount': row[1],
                    'type': row[2],
                    'method': row[3],
                    'beneficiary': row[4] or '',
                    'notes': row[5] or ''
                })
            
            conn.close()
            return outflows
            
        except Exception as e:
            print(f"خطأ في جلب التدفقات الخارجة: {str(e)}")
            return []

class CashFlowWindow(QMainWindow):
    """نافذة التدفقات النقدية"""
    
    def __init__(self):
        super().__init__()
        self.manager = CashFlowManager()
        self.init_ui()
        self.load_cash_flow_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💸 تقرير التدفقات النقدية (Cash Flow)")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("💸 تقرير التدفقات النقدية (Cash Flow)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # شريط أدوات التحكم
        self.create_controls_bar(main_layout)

        # مجموعة الأزرار
        self.create_buttons_group(main_layout)

        # منطقة عرض التقرير
        self.create_report_area(main_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام التدفقات النقدية")
    
    def create_controls_bar(self, main_layout):
        """إنشاء شريط أدوات التحكم"""
        controls_group = QGroupBox("🎛️ فترة التقرير")
        controls_group.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout = QHBoxLayout(controls_group)

        # تاريخ البداية
        start_label = QLabel("من تاريخ:")
        start_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(start_label)

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(self.start_date)

        # تاريخ النهاية
        end_label = QLabel("إلى تاريخ:")
        end_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(end_label)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(self.end_date)

        # أزرار سريعة
        today_btn = QPushButton("اليوم")
        today_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        today_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        today_btn.clicked.connect(self.set_today)
        controls_layout.addWidget(today_btn)

        week_btn = QPushButton("هذا الأسبوع")
        week_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        week_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)
        week_btn.clicked.connect(self.set_this_week)
        controls_layout.addWidget(week_btn)

        month_btn = QPushButton("هذا الشهر")
        month_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        month_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border-radius: 6px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #e55a00;
            }
        """)
        month_btn.clicked.connect(self.set_this_month)
        controls_layout.addWidget(month_btn)

        main_layout.addWidget(controls_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_cash_flow_data)
        buttons_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_btn)

        main_layout.addWidget(buttons_group)

    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير"""
        # تخطيط أفقي للجداول والملخص
        tables_layout = QHBoxLayout()

        # جدول التدفقات الداخلة
        inflows_group = QGroupBox("� التدفقات الداخلة")
        inflows_group.setFont(QFont("Calibri", 14, QFont.Bold))
        inflows_layout = QVBoxLayout(inflows_group)

        self.inflows_table = QTableWidget()
        self.inflows_table.setColumnCount(5)
        self.inflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "المصدر", "طريقة الدفع", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الداخلة
        inflows_header = self.inflows_table.horizontalHeader()
        inflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        inflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #27ae60;
                color: white;
                padding: 8px;
                border: 1px solid #229954;
                font-weight: bold;
            }
        """)

        self.inflows_table.setColumnWidth(0, 100)  # التاريخ
        self.inflows_table.setColumnWidth(1, 120)  # المبلغ
        self.inflows_table.setColumnWidth(2, 150)  # المصدر
        self.inflows_table.setColumnWidth(3, 120)  # طريقة الدفع
        self.inflows_table.setColumnWidth(4, 200)  # ملاحظات

        self.inflows_table.setAlternatingRowColors(True)
        self.inflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        inflows_layout.addWidget(self.inflows_table)
        tables_layout.addWidget(inflows_group)

        # جدول التدفقات الخارجة
        outflows_group = QGroupBox("💸 التدفقات الخارجة")
        outflows_group.setFont(QFont("Calibri", 14, QFont.Bold))
        outflows_layout = QVBoxLayout(outflows_group)

        self.outflows_table = QTableWidget()
        self.outflows_table.setColumnCount(6)
        self.outflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "نوع المصروف", "طريقة الدفع", "المستفيد", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الخارجة
        outflows_header = self.outflows_table.horizontalHeader()
        outflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        outflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 8px;
                border: 1px solid #c0392b;
                font-weight: bold;
            }
        """)

        self.outflows_table.setColumnWidth(0, 100)  # التاريخ
        self.outflows_table.setColumnWidth(1, 120)  # المبلغ
        self.outflows_table.setColumnWidth(2, 150)  # نوع المصروف
        self.outflows_table.setColumnWidth(3, 120)  # طريقة الدفع
        self.outflows_table.setColumnWidth(4, 150)  # المستفيد
        self.outflows_table.setColumnWidth(5, 200)  # ملاحظات

        self.outflows_table.setAlternatingRowColors(True)
        self.outflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        outflows_layout.addWidget(self.outflows_table)
        tables_layout.addWidget(outflows_group)

        main_layout.addLayout(tables_layout)

        # ملخص التدفقات النقدية
        summary_group = QGroupBox("📊 ملخص التدفقات النقدية")
        summary_group.setFont(QFont("Calibri", 14, QFont.Bold))
        summary_layout = QVBoxLayout(summary_group)

        self.summary_text = QTextEdit()
        self.summary_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.summary_text.setFixedHeight(300)
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب التدفقات الداخلة
        self.create_inflows_tab()

        # تبويب التدفقات الخارجة
        self.create_outflows_tab()

        # تبويب ملخصات التدفقات النقدية
        self.create_summary_tab()

        main_layout.addWidget(self.tab_widget)

    def create_inflows_tab(self):
        """إنشاء تبويب التدفقات الداخلة"""
        inflows_tab = QWidget()
        inflows_layout = QVBoxLayout(inflows_tab)

        # عنوان التبويب
        title_label = QLabel("💰 التدفقات الداخلة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #27ae60;
                color: white;
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        inflows_layout.addWidget(title_label)

        # جدول التدفقات الداخلة
        self.inflows_table = QTableWidget()
        self.inflows_table.setColumnCount(5)
        self.inflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "المصدر", "طريقة الدفع", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الداخلة
        inflows_header = self.inflows_table.horizontalHeader()
        inflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        inflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 10px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        self.inflows_table.setColumnWidth(0, 120)  # التاريخ
        self.inflows_table.setColumnWidth(1, 150)  # المبلغ
        self.inflows_table.setColumnWidth(2, 200)  # المصدر
        self.inflows_table.setColumnWidth(3, 150)  # طريقة الدفع
        self.inflows_table.setColumnWidth(4, 300)  # ملاحظات

        self.inflows_table.setAlternatingRowColors(True)
        self.inflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.inflows_table.setFont(QFont("Calibri", 12, QFont.Bold))

        inflows_layout.addWidget(self.inflows_table)

        # إضافة مخطط بياني للتدفقات الداخلة
        if MATPLOTLIB_AVAILABLE:
            self.create_inflows_chart(inflows_layout)

        # إضافة التبويب
        self.tab_widget.addTab(inflows_tab, "💰 التدفقات الداخلة")

    def create_outflows_tab(self):
        """إنشاء تبويب التدفقات الخارجة"""
        outflows_tab = QWidget()
        outflows_layout = QVBoxLayout(outflows_tab)

        # عنوان التبويب
        title_label = QLabel("💸 التدفقات الخارجة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        outflows_layout.addWidget(title_label)

        # جدول التدفقات الخارجة
        self.outflows_table = QTableWidget()
        self.outflows_table.setColumnCount(6)
        self.outflows_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "نوع المصروف", "طريقة الدفع", "المستفيد", "ملاحظات"
        ])

        # تنسيق جدول التدفقات الخارجة
        outflows_header = self.outflows_table.horizontalHeader()
        outflows_header.setFont(QFont("Calibri", 13, QFont.Bold))
        outflows_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff8c00;
                color: white;
                padding: 10px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        self.outflows_table.setColumnWidth(0, 120)  # التاريخ
        self.outflows_table.setColumnWidth(1, 150)  # المبلغ
        self.outflows_table.setColumnWidth(2, 180)  # نوع المصروف
        self.outflows_table.setColumnWidth(3, 150)  # طريقة الدفع
        self.outflows_table.setColumnWidth(4, 200)  # المستفيد
        self.outflows_table.setColumnWidth(5, 300)  # ملاحظات

        self.outflows_table.setAlternatingRowColors(True)
        self.outflows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.outflows_table.setFont(QFont("Calibri", 12, QFont.Bold))

        outflows_layout.addWidget(self.outflows_table)

        # إضافة مخطط بياني للتدفقات الخارجة
        if MATPLOTLIB_AVAILABLE:
            self.create_outflows_chart(outflows_layout)

        # إضافة التبويب
        self.tab_widget.addTab(outflows_tab, "💸 التدفقات الخارجة")

    def create_summary_tab(self):
        """إنشاء تبويب ملخصات التدفقات النقدية"""
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)

        # عنوان التبويب
        title_label = QLabel("📊 ملخصات التدفقات النقدية")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #9b59b6;
                color: white;
                padding: 12px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        summary_layout.addWidget(title_label)

        # منطقة النص للملخص
        self.summary_text = QTextEdit()
        self.summary_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.summary_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        summary_layout.addWidget(self.summary_text)

        # إضافة مخططات بيانية للملخص
        if MATPLOTLIB_AVAILABLE:
            self.create_summary_charts(summary_layout)

        # إضافة التبويب
        self.tab_widget.addTab(summary_tab, "📊 ملخصات التدفقات النقدية")

    def create_inflows_chart(self, layout):
        """إنشاء مخطط بياني للتدفقات الداخلة"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

            # إنشاء الرسم البياني
            fig = Figure(figsize=(10, 4), dpi=100)
            canvas = FigureCanvas(fig)

            # إضافة الرسم البياني إلى التخطيط
            layout.addWidget(canvas)

            # حفظ المرجع للتحديث لاحقاً
            self.inflows_chart = fig
            self.inflows_canvas = canvas

        except Exception as e:
            print(f"خطأ في إنشاء مخطط التدفقات الداخلة: {str(e)}")

    def create_outflows_chart(self, layout):
        """إنشاء مخطط بياني للتدفقات الخارجة"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

            # إنشاء الرسم البياني
            fig = Figure(figsize=(10, 4), dpi=100)
            canvas = FigureCanvas(fig)

            # إضافة الرسم البياني إلى التخطيط
            layout.addWidget(canvas)

            # حفظ المرجع للتحديث لاحقاً
            self.outflows_chart = fig
            self.outflows_canvas = canvas

        except Exception as e:
            print(f"خطأ في إنشاء مخطط التدفقات الخارجة: {str(e)}")

    def create_summary_charts(self, layout):
        """إنشاء مخططات بيانية للملخص"""
        try:
            from matplotlib.figure import Figure
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

            # إنشاء الرسم البياني
            fig = Figure(figsize=(12, 6), dpi=100)
            canvas = FigureCanvas(fig)

            # إضافة الرسم البياني إلى التخطيط
            layout.addWidget(canvas)

            # حفظ المرجع للتحديث لاحقاً
            self.summary_chart = fig
            self.summary_canvas = canvas

        except Exception as e:
            print(f"خطأ في إنشاء مخططات الملخص: {str(e)}")

    def update_charts(self, inflows, outflows):
        """تحديث المخططات البيانية"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            # تحديث مخطط التدفقات الداخلة
            if hasattr(self, 'inflows_chart'):
                self.update_inflows_chart(inflows)

            # تحديث مخطط التدفقات الخارجة
            if hasattr(self, 'outflows_chart'):
                self.update_outflows_chart(outflows)

            # تحديث مخططات الملخص
            if hasattr(self, 'summary_chart'):
                self.update_summary_chart(inflows, outflows)

        except Exception as e:
            print(f"خطأ في تحديث المخططات: {str(e)}")

    def update_inflows_chart(self, inflows):
        """تحديث مخطط التدفقات الداخلة"""
        try:
            self.inflows_chart.clear()
            ax = self.inflows_chart.add_subplot(111)

            if inflows:
                # تجميع البيانات حسب المصدر
                sources = {}
                for inflow in inflows:
                    source = inflow['source']
                    if source not in sources:
                        sources[source] = 0
                    sources[source] += inflow['amount']

                # إنشاء الرسم البياني الدائري
                labels = list(sources.keys())
                sizes = list(sources.values())
                colors = ['#27ae60', '#2ecc71', '#58d68d', '#82e0aa', '#abebc6']

                ax.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
                ax.set_title('توزيع التدفقات الداخلة حسب المصدر', fontsize=14, fontweight='bold')
            else:
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('التدفقات الداخلة', fontsize=14, fontweight='bold')

            self.inflows_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث مخطط التدفقات الداخلة: {str(e)}")

    def update_outflows_chart(self, outflows):
        """تحديث مخطط التدفقات الخارجة"""
        try:
            self.outflows_chart.clear()
            ax = self.outflows_chart.add_subplot(111)

            if outflows:
                # تجميع البيانات حسب نوع المصروف
                types = {}
                for outflow in outflows:
                    expense_type = outflow['type']
                    if expense_type not in types:
                        types[expense_type] = 0
                    types[expense_type] += outflow['amount']

                # إنشاء الرسم البياني الدائري
                labels = list(types.keys())
                sizes = list(types.values())
                colors = ['#e74c3c', '#ec7063', '#f1948a', '#f5b7b1', '#fadbd8']

                ax.pie(sizes, labels=labels, colors=colors[:len(labels)], autopct='%1.1f%%', startangle=90)
                ax.set_title('توزيع التدفقات الخارجة حسب النوع', fontsize=14, fontweight='bold')
            else:
                ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', ha='center', va='center', transform=ax.transAxes)
                ax.set_title('التدفقات الخارجة', fontsize=14, fontweight='bold')

            self.outflows_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث مخطط التدفقات الخارجة: {str(e)}")

    def update_summary_chart(self, inflows, outflows):
        """تحديث مخططات الملخص"""
        try:
            self.summary_chart.clear()

            # حساب الإجماليات
            total_inflows = sum(inflow['amount'] for inflow in inflows)
            total_outflows = sum(outflow['amount'] for outflow in outflows)

            # الرسم البياني الأول: مقارنة الإجماليات
            ax1 = self.summary_chart.add_subplot(121)
            categories = ['التدفقات الداخلة', 'التدفقات الخارجة']
            amounts = [total_inflows, total_outflows]
            colors = ['#27ae60', '#e74c3c']

            bars = ax1.bar(categories, amounts, color=colors)
            ax1.set_title('مقارنة التدفقات النقدية', fontsize=14, fontweight='bold')
            ax1.set_ylabel('المبلغ (درهم)')

            # إضافة قيم على الأعمدة
            for bar, amount in zip(bars, amounts):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{amount:,.0f}', ha='center', va='bottom')

            # الرسم البياني الثاني: صافي التدفق النقدي
            ax2 = self.summary_chart.add_subplot(122)
            net_flow = total_inflows - total_outflows
            color = '#27ae60' if net_flow >= 0 else '#e74c3c'

            ax2.bar(['صافي التدفق النقدي'], [net_flow], color=color)
            ax2.set_title('صافي التدفق النقدي', fontsize=14, fontweight='bold')
            ax2.set_ylabel('المبلغ (درهم)')

            # إضافة قيمة على العمود
            ax2.text(0, net_flow, f'{net_flow:,.0f}', ha='center',
                    va='bottom' if net_flow >= 0 else 'top')

            self.summary_chart.tight_layout()
            self.summary_canvas.draw()

        except Exception as e:
            print(f"خطأ في تحديث مخططات الملخص: {str(e)}")

    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = QDate.currentDate()
        self.start_date.setDate(today)
        self.end_date.setDate(today)

    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = QDate.currentDate()
        start_of_week = today.addDays(-today.dayOfWeek() + 1)
        self.start_date.setDate(start_of_week)
        self.end_date.setDate(today)

    def set_this_month(self):
        """تعيين هذا الشهر"""
        today = QDate.currentDate()
        start_of_month = QDate(today.year(), today.month(), 1)
        self.start_date.setDate(start_of_month)
        self.end_date.setDate(today)

    def load_cash_flow_data(self):
        """تحميل بيانات التدفقات النقدية"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            # جلب البيانات
            inflows = self.manager.get_cash_inflows(start_date, end_date)
            outflows = self.manager.get_cash_outflows(start_date, end_date)

            # تحديث الجداول
            self.update_inflows_table(inflows)
            self.update_outflows_table(outflows)

            # إنشاء ملخص التدفقات
            self.generate_cash_flow_summary(inflows, outflows, start_date, end_date)

            # تحديث المخططات البيانية
            self.update_charts(inflows, outflows)

            # تحديث شريط الحالة
            self.statusBar().showMessage(f"تم تحميل التدفقات النقدية من {start_date} إلى {end_date}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التدفقات النقدية: {str(e)}")

    def update_inflows_table(self, inflows):
        """تحديث جدول التدفقات الداخلة"""
        self.inflows_table.setRowCount(len(inflows))

        for row, inflow in enumerate(inflows):
            items = [
                inflow['date'],
                f"{inflow['amount']:,.2f} درهم",
                inflow['source'],
                inflow['method'],
                inflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 10))

                # تلوين المبلغ بالأخضر
                if col == 1:
                    item.setForeground(QColor("#27ae60"))
                    item.setFont(QFont("Calibri", 10, QFont.Bold))

                self.inflows_table.setItem(row, col, item)

    def update_outflows_table(self, outflows):
        """تحديث جدول التدفقات الخارجة"""
        self.outflows_table.setRowCount(len(outflows))

        for row, outflow in enumerate(outflows):
            items = [
                outflow['date'],
                f"{outflow['amount']:,.2f} درهم",
                outflow['type'],
                outflow['method'],
                outflow['beneficiary'],
                outflow['notes']
            ]

            for col, value in enumerate(items):
                item = QTableWidgetItem(str(value))
                item.setFont(QFont("Calibri", 10))

                # تلوين المبلغ بالأحمر
                if col == 1:
                    item.setForeground(QColor("#e74c3c"))
                    item.setFont(QFont("Calibri", 10, QFont.Bold))

                self.outflows_table.setItem(row, col, item)

    def generate_cash_flow_summary(self, inflows, outflows, start_date, end_date):
        """إنشاء ملخص التدفقات النقدية"""
        try:
            # حساب الإجماليات
            total_inflows = sum(inflow['amount'] for inflow in inflows)
            total_outflows = sum(outflow['amount'] for outflow in outflows)
            net_cash_flow = total_inflows - total_outflows

            # بناء التقرير
            summary = f"💸 تقرير التدفقات النقدية (Cash Flow)\n"
            summary += f"الفترة: من {start_date} إلى {end_date}\n"
            summary += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            summary += "=" * 60 + "\n\n"

            # ملخص التدفقات الداخلة
            summary += "💰 التدفقات الداخلة:\n"
            summary += "-" * 30 + "\n"

            # تجميع التدفقات الداخلة حسب المصدر
            inflow_sources = {}
            for inflow in inflows:
                source = inflow['source']
                if source not in inflow_sources:
                    inflow_sources[source] = 0
                inflow_sources[source] += inflow['amount']

            for source, amount in inflow_sources.items():
                summary += f"   {source}: {amount:,.2f} درهم\n"

            summary += "-" * 30 + "\n"
            summary += f"   إجمالي التدفقات الداخلة: {total_inflows:,.2f} درهم\n\n"

            # ملخص التدفقات الخارجة
            summary += "💸 التدفقات الخارجة:\n"
            summary += "-" * 30 + "\n"

            # تجميع التدفقات الخارجة حسب النوع
            outflow_types = {}
            for outflow in outflows:
                expense_type = outflow['type']
                if expense_type not in outflow_types:
                    outflow_types[expense_type] = 0
                outflow_types[expense_type] += outflow['amount']

            for expense_type, amount in outflow_types.items():
                summary += f"   {expense_type}: {amount:,.2f} درهم\n"

            summary += "-" * 30 + "\n"
            summary += f"   إجمالي التدفقات الخارجة: {total_outflows:,.2f} درهم\n\n"

            # صافي التدفق النقدي
            summary += "📊 صافي التدفق النقدي:\n"
            summary += "=" * 30 + "\n"

            if net_cash_flow > 0:
                summary += f"   صافي التدفق الإيجابي: {net_cash_flow:,.2f} درهم ✅\n"
                summary += f"   الحالة: تدفق نقدي إيجابي (سيولة جيدة)\n"
            elif net_cash_flow < 0:
                summary += f"   صافي التدفق السلبي: {abs(net_cash_flow):,.2f} درهم ❌\n"
                summary += f"   الحالة: تدفق نقدي سلبي (نقص في السيولة)\n"
            else:
                summary += f"   صافي التدفق: {net_cash_flow:,.2f} درهم ⚖️\n"
                summary += f"   الحالة: متوازن\n"

            # إحصائيات إضافية
            summary += "\n📈 إحصائيات إضافية:\n"
            summary += "-" * 30 + "\n"
            summary += f"   عدد المعاملات الداخلة: {len(inflows)}\n"
            summary += f"   عدد المعاملات الخارجة: {len(outflows)}\n"
            summary += f"   إجمالي المعاملات: {len(inflows) + len(outflows)}\n"

            if len(inflows) > 0:
                avg_inflow = total_inflows / len(inflows)
                summary += f"   متوسط المعاملة الداخلة: {avg_inflow:,.2f} درهم\n"

            if len(outflows) > 0:
                avg_outflow = total_outflows / len(outflows)
                summary += f"   متوسط المعاملة الخارجة: {avg_outflow:,.2f} درهم\n"

            # نسب مالية
            if total_inflows > 0:
                outflow_ratio = (total_outflows / total_inflows) * 100
                summary += f"   نسبة التدفقات الخارجة إلى الداخلة: {outflow_ratio:.2f}%\n"

            self.summary_text.setPlainText(summary)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ملخص التدفقات: {str(e)}")

    def print_report(self):
        """طباعة تقرير التدفقات النقدية"""
        try:
            # استيراد print101 للطباعة
            import print101

            # جلب بيانات المؤسسة
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            cursor.execute("SELECT المؤسسة, ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            institution_data = cursor.fetchone()

            institution_name = institution_data[0] if institution_data else "المؤسسة التعليمية"
            logo_path = institution_data[1] if institution_data and institution_data[1] else ""

            # جلب بيانات التدفقات
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")

            inflows = self.manager.get_cash_inflows(start_date, end_date)
            outflows = self.manager.get_cash_outflows(start_date, end_date)

            # حساب الإجماليات
            total_inflows = sum(inflow['amount'] for inflow in inflows)
            total_outflows = sum(outflow['amount'] for outflow in outflows)
            net_cash_flow = total_inflows - total_outflows

            # إنشاء التقرير
            report_data = {
                'title': 'تقرير التدفقات النقدية (Cash Flow)',
                'institution_name': institution_name,
                'logo_path': logo_path,
                'period': f"من {start_date} إلى {end_date}",
                'inflows': inflows,
                'outflows': outflows,
                'total_inflows': total_inflows,
                'total_outflows': total_outflows,
                'net_cash_flow': net_cash_flow,
                'generation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # طباعة التقرير
            print101.print_cash_flow_report(report_data)

            conn.close()

        except ImportError:
            QMessageBox.warning(self, "تحذير", "وحدة الطباعة غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def show_daily_flow_chart(self):
        """عرض رسم بياني للتدفق اليومي"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم التدفق اليومي سيكون متاحاً قريباً")

    def show_cumulative_chart(self):
        """عرض رسم بياني للتدفق التراكمي"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم التدفق التراكمي سيكون متاحاً قريباً")

    def show_comparison_chart(self):
        """عرض رسم بياني لمقارنة التدفقات"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم مقارنة التدفقات سيكون متاحاً قريباً")



if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = CashFlowWindow()
    window.show()

    sys.exit(app.exec_())
