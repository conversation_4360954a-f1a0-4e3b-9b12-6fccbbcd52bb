# ================= نافذة تقارير الأرباح والخسائر (P&L) =================
# ملف منفصل لإدارة تقارير الأرباح والخسائر للمؤسسة التعليمية
# يحتوي على: تقارير شهرية/فصلية/سنوية، الإيرادات والمصاريف، الرسوم البيانية

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. الرسوم البيانية ستكون معطلة.")

class ProfitLossManager:
    """مدير تقارير الأرباح والخسائر"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    
    def get_revenue_data(self, year, month=None, quarter=None):
        """جلب بيانات الإيرادات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            revenue_data = {}
            
            # إيرادات التسجيل
            if month:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM registration_fees 
                    WHERE strftime('%Y', payment_date) = ? 
                    AND strftime('%m', payment_date) = ?
                """, (str(year), f"{month:02d}"))
            else:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM registration_fees 
                    WHERE strftime('%Y', payment_date) = ?
                """, (str(year),))
            
            registration_revenue = cursor.fetchone()[0] or 0
            revenue_data['رسوم التسجيل'] = registration_revenue
            
            # إيرادات الواجبات الشهرية
            if month:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM monthly_duties 
                    WHERE year = ? AND 
                    CASE month 
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END = ?
                """, (year, month))
            else:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM monthly_duties 
                    WHERE year = ?
                """, (year,))
            
            monthly_revenue = cursor.fetchone()[0] or 0
            revenue_data['الواجبات الشهرية'] = monthly_revenue
            
            conn.close()
            return revenue_data
            
        except Exception as e:
            print(f"خطأ في جلب بيانات الإيرادات: {str(e)}")
            return {}
    
    def get_expense_data(self, year, month=None, quarter=None):
        """جلب بيانات المصاريف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if month:
                cursor.execute("""
                    SELECT نوع_المصروف, SUM(المبلغ) 
                    FROM المصاريف 
                    WHERE strftime('%Y', التاريخ) = ? 
                    AND strftime('%m', التاريخ) = ?
                    GROUP BY نوع_المصروف
                """, (str(year), f"{month:02d}"))
            else:
                cursor.execute("""
                    SELECT نوع_المصروف, SUM(المبلغ) 
                    FROM المصاريف 
                    WHERE strftime('%Y', التاريخ) = ?
                    GROUP BY نوع_المصروف
                """, (str(year),))
            
            expense_data = {}
            for row in cursor.fetchall():
                expense_type, amount = row
                expense_data[expense_type] = amount or 0
            
            conn.close()
            return expense_data
            
        except Exception as e:
            print(f"خطأ في جلب بيانات المصاريف: {str(e)}")
            return {}

class ProfitLossWindow(QMainWindow):
    """نافذة تقارير الأرباح والخسائر"""
    
    def __init__(self):
        super().__init__()
        self.manager = ProfitLossManager()
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.init_ui()
        self.load_report_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📈 تقارير الأرباح والخسائر (P&L)")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("📈 تقارير الأرباح والخسائر (P&L)")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # شريط أدوات التحكم
        self.create_controls_bar(main_layout)

        # مجموعة الأزرار
        self.create_buttons_group(main_layout)

        # منطقة عرض التقرير
        self.create_report_area(main_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage(f"جاهز - نظام تقارير الأرباح والخسائر {self.current_year}")
    
    def create_controls_bar(self, main_layout):
        """إنشاء شريط أدوات التحكم"""
        controls_group = QGroupBox("🎛️ إعدادات التقرير")
        controls_group.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout = QHBoxLayout(controls_group)

        # نوع التقرير
        report_type_label = QLabel("نوع التقرير:")
        report_type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(report_type_label)

        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["شهري", "فصلي", "سنوي"])
        self.report_type_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.report_type_combo.currentTextChanged.connect(self.report_type_changed)
        controls_layout.addWidget(self.report_type_combo)

        # السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(year_label)

        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(self.year_spin)

        # الشهر
        self.month_label = QLabel("الشهر:")
        self.month_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(self.month_label)

        self.month_combo = QComboBox()
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(self.current_month - 1)
        self.month_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(self.month_combo)

        # الفصل
        self.quarter_label = QLabel("الفصل:")
        self.quarter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        controls_layout.addWidget(self.quarter_label)

        self.quarter_combo = QComboBox()
        self.quarter_combo.addItems(["الفصل الأول", "الفصل الثاني", "الفصل الثالث", "الفصل الرابع"])
        self.quarter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_layout.addWidget(self.quarter_combo)

        main_layout.addWidget(controls_group)

        # إخفاء/إظهار العناصر حسب نوع التقرير
        self.report_type_changed()

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر تحديث التقرير
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_report_data)
        buttons_layout.addWidget(refresh_btn)

        # زر طباعة التقرير
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_btn)

        main_layout.addWidget(buttons_group)

    def create_report_area(self, main_layout):
        """إنشاء منطقة عرض التقرير"""
        report_group = QGroupBox("📊 التقرير المالي")
        report_group.setFont(QFont("Calibri", 14, QFont.Bold))
        report_layout = QVBoxLayout(report_group)

        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setFont(QFont("Calibri", 13, QFont.Bold))
        self.report_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        report_layout.addWidget(self.report_text)

        main_layout.addWidget(report_group)
    




    def report_type_changed(self):
        """تغيير نوع التقرير"""
        report_type = self.report_type_combo.currentText()

        if report_type == "شهري":
            self.month_label.show()
            self.month_combo.show()
            self.quarter_label.hide()
            self.quarter_combo.hide()
        elif report_type == "فصلي":
            self.month_label.hide()
            self.month_combo.hide()
            self.quarter_label.show()
            self.quarter_combo.show()
        else:  # سنوي
            self.month_label.hide()
            self.month_combo.hide()
            self.quarter_label.hide()
            self.quarter_combo.hide()

    def load_report_data(self):
        """تحميل بيانات التقرير"""
        try:
            report_type = self.report_type_combo.currentText()
            year = self.year_spin.value()
            month = None
            quarter = None

            if report_type == "شهري":
                month = self.month_combo.currentIndex() + 1
            elif report_type == "فصلي":
                quarter = self.quarter_combo.currentIndex() + 1

            # جلب بيانات الإيرادات والمصاريف
            revenue_data = self.manager.get_revenue_data(year, month, quarter)
            expense_data = self.manager.get_expense_data(year, month, quarter)

            # إنشاء التقرير
            self.generate_profit_loss_report(revenue_data, expense_data, year, month, quarter)

            # تحديث شريط الحالة
            period_text = ""
            if month:
                months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                period_text = f"شهر {months[month-1]} {year}"
            elif quarter:
                period_text = f"الفصل {quarter} من سنة {year}"
            else:
                period_text = f"سنة {year}"

            self.statusBar().showMessage(f"تم تحميل تقرير الأرباح والخسائر لـ {period_text}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التقرير: {str(e)}")

    def generate_profit_loss_report(self, revenue_data, expense_data, year, month=None, quarter=None):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            # تحديد الفترة
            period_text = ""
            if month:
                months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                period_text = f"شهر {months[month-1]} {year}"
            elif quarter:
                period_text = f"الفصل {quarter} من سنة {year}"
            else:
                period_text = f"سنة {year}"

            # بناء التقرير
            report = f"📈 تقرير الأرباح والخسائر (P&L)\n"
            report += f"الفترة: {period_text}\n"
            report += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            report += "=" * 60 + "\n\n"

            # قسم الإيرادات
            report += "💰 الإيرادات:\n"
            report += "-" * 30 + "\n"

            total_revenue = 0
            for revenue_type, amount in revenue_data.items():
                report += f"   {revenue_type}: {amount:,.2f} درهم\n"
                total_revenue += amount

            report += "-" * 30 + "\n"
            report += f"   إجمالي الإيرادات: {total_revenue:,.2f} درهم\n\n"

            # قسم المصاريف
            report += "💸 المصاريف:\n"
            report += "-" * 30 + "\n"

            total_expenses = 0
            for expense_type, amount in expense_data.items():
                report += f"   {expense_type}: {amount:,.2f} درهم\n"
                total_expenses += amount

            report += "-" * 30 + "\n"
            report += f"   إجمالي المصاريف: {total_expenses:,.2f} درهم\n\n"

            # حساب الربح/الخسارة
            net_profit = total_revenue - total_expenses

            report += "📊 النتيجة النهائية:\n"
            report += "=" * 30 + "\n"

            if net_profit > 0:
                report += f"   الربح الصافي: {net_profit:,.2f} درهم ✅\n"
                profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
                report += f"   هامش الربح: {profit_margin:.2f}%\n"
            elif net_profit < 0:
                report += f"   الخسارة الصافية: {abs(net_profit):,.2f} درهم ❌\n"
                loss_margin = (abs(net_profit) / total_revenue * 100) if total_revenue > 0 else 0
                report += f"   نسبة الخسارة: {loss_margin:.2f}%\n"
            else:
                report += f"   التوازن: {net_profit:,.2f} درهم ⚖️\n"
                report += f"   الحالة: متوازن (لا ربح ولا خسارة)\n"

            # إضافة مؤشرات أداء إضافية
            report += "\n📈 مؤشرات الأداء:\n"
            report += "-" * 30 + "\n"

            if total_revenue > 0:
                expense_ratio = (total_expenses / total_revenue * 100)
                report += f"   نسبة المصاريف إلى الإيرادات: {expense_ratio:.2f}%\n"

                if expense_ratio < 70:
                    report += f"   تقييم الأداء: ممتاز ✅\n"
                elif expense_ratio < 85:
                    report += f"   تقييم الأداء: جيد 👍\n"
                elif expense_ratio < 95:
                    report += f"   تقييم الأداء: مقبول ⚠️\n"
                else:
                    report += f"   تقييم الأداء: يحتاج تحسين ❌\n"

            self.report_text.setPlainText(report)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def show_revenue_chart(self):
        """عرض رسم بياني للإيرادات"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم الإيرادات سيكون متاحاً قريباً")

    def show_expense_chart(self):
        """عرض رسم بياني للمصاريف"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم المصاريف سيكون متاحاً قريباً")

    def show_profit_chart(self):
        """عرض رسم بياني للأرباح"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم الأرباح سيكون متاحاً قريباً")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "طباعة التقرير ستكون متاحة قريباً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = ProfitLossWindow()
    window.show()

    sys.exit(app.exec_())
