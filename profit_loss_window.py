# ================= نافذة تقارير الأرباح والخسائر (P&L) =================
# ملف منفصل لإدارة تقارير الأرباح والخسائر للمؤسسة التعليمية
# يحتوي على: تقارير شهرية/فصلية/سنوية، الإيرادات والمصاريف، الرسوم البيانية

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# تعيين الخط العربي لـ matplotlib
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

class ProfitLossManager:
    """مدير تقارير الأرباح والخسائر"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
    
    def get_revenue_data(self, year, month=None, quarter=None):
        """جلب بيانات الإيرادات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            revenue_data = {}
            
            # إيرادات التسجيل
            if month:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM registration_fees 
                    WHERE strftime('%Y', payment_date) = ? 
                    AND strftime('%m', payment_date) = ?
                """, (str(year), f"{month:02d}"))
            else:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM registration_fees 
                    WHERE strftime('%Y', payment_date) = ?
                """, (str(year),))
            
            registration_revenue = cursor.fetchone()[0] or 0
            revenue_data['رسوم التسجيل'] = registration_revenue
            
            # إيرادات الواجبات الشهرية
            if month:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM monthly_duties 
                    WHERE year = ? AND 
                    CASE month 
                        WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                    END = ?
                """, (year, month))
            else:
                cursor.execute("""
                    SELECT SUM(amount_paid) FROM monthly_duties 
                    WHERE year = ?
                """, (year,))
            
            monthly_revenue = cursor.fetchone()[0] or 0
            revenue_data['الواجبات الشهرية'] = monthly_revenue
            
            conn.close()
            return revenue_data
            
        except Exception as e:
            print(f"خطأ في جلب بيانات الإيرادات: {str(e)}")
            return {}
    
    def get_expense_data(self, year, month=None, quarter=None):
        """جلب بيانات المصاريف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if month:
                cursor.execute("""
                    SELECT نوع_المصروف, SUM(المبلغ) 
                    FROM المصاريف 
                    WHERE strftime('%Y', التاريخ) = ? 
                    AND strftime('%m', التاريخ) = ?
                    GROUP BY نوع_المصروف
                """, (str(year), f"{month:02d}"))
            else:
                cursor.execute("""
                    SELECT نوع_المصروف, SUM(المبلغ) 
                    FROM المصاريف 
                    WHERE strftime('%Y', التاريخ) = ?
                    GROUP BY نوع_المصروف
                """, (str(year),))
            
            expense_data = {}
            for row in cursor.fetchall():
                expense_type, amount = row
                expense_data[expense_type] = amount or 0
            
            conn.close()
            return expense_data
            
        except Exception as e:
            print(f"خطأ في جلب بيانات المصاريف: {str(e)}")
            return {}

class ProfitLossWindow(QMainWindow):
    """نافذة تقارير الأرباح والخسائر"""
    
    def __init__(self):
        super().__init__()
        self.manager = ProfitLossManager()
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.init_ui()
        self.load_report_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📈 تقارير الأرباح والخسائر (P&L)")
        self.setGeometry(100, 100, 1600, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        self.center_window(self)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("📈 تقارير الأرباح والخسائر (P&L)")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # شريط أدوات التحكم
        self.create_controls_bar(main_layout)
        
        # تبويبات التقارير
        tabs = QTabWidget()
        tabs.setFont(QFont("Calibri", 11, QFont.Bold))
        
        # تبويب التقرير المالي
        self.create_financial_report_tab(tabs)
        
        # تبويب الرسوم البيانية
        self.create_charts_tab(tabs)
        
        # تبويب التحليل المقارن
        self.create_analysis_tab(tabs)
        
        main_layout.addWidget(tabs)
        
        # شريط الحالة
        self.statusBar().showMessage(f"جاهز لعرض تقارير الأرباح والخسائر للسنة {self.current_year}")
        self.statusBar().setFont(QFont("Calibri", 10))
    
    def create_controls_bar(self, parent_layout):
        """إنشاء شريط أدوات التحكم"""
        controls_group = QGroupBox("🎛️ أدوات التحكم")
        controls_group.setFont(QFont("Calibri", 12, QFont.Bold))
        controls_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #34495e;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #34495e;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_group)
        
        # نوع التقرير
        report_type_label = QLabel("📊 نوع التقرير:")
        report_type_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["شهري", "فصلي", "سنوي"])
        self.report_type_combo.setFont(QFont("Calibri", 11))
        self.report_type_combo.setStyleSheet(self.get_combo_style())
        self.report_type_combo.currentTextChanged.connect(self.report_type_changed)
        
        # السنة
        year_label = QLabel("📅 السنة:")
        year_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setFont(QFont("Calibri", 11))
        self.year_spin.setStyleSheet(self.get_input_style())
        
        # الشهر
        self.month_label = QLabel("📆 الشهر:")
        self.month_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.month_combo = QComboBox()
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(self.current_month - 1)
        self.month_combo.setFont(QFont("Calibri", 11))
        self.month_combo.setStyleSheet(self.get_combo_style())
        
        # الفصل
        self.quarter_label = QLabel("📈 الفصل:")
        self.quarter_label.setFont(QFont("Calibri", 11, QFont.Bold))
        
        self.quarter_combo = QComboBox()
        self.quarter_combo.addItems(["الفصل الأول", "الفصل الثاني", "الفصل الثالث", "الفصل الرابع"])
        self.quarter_combo.setFont(QFont("Calibri", 11))
        self.quarter_combo.setStyleSheet(self.get_combo_style())
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث التقرير")
        refresh_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        refresh_btn.setStyleSheet(self.get_button_style("#3498db"))
        refresh_btn.clicked.connect(self.load_report_data)
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التقرير")
        print_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        print_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        print_btn.clicked.connect(self.print_report)
        
        # إضافة العناصر للتخطيط
        controls_layout.addWidget(report_type_label)
        controls_layout.addWidget(self.report_type_combo)
        controls_layout.addWidget(year_label)
        controls_layout.addWidget(self.year_spin)
        controls_layout.addWidget(self.month_label)
        controls_layout.addWidget(self.month_combo)
        controls_layout.addWidget(self.quarter_label)
        controls_layout.addWidget(self.quarter_combo)
        controls_layout.addWidget(refresh_btn)
        controls_layout.addWidget(print_btn)
        controls_layout.addStretch()
        
        parent_layout.addWidget(controls_group)
        
        # إخفاء/إظهار العناصر حسب نوع التقرير
        self.report_type_changed()
    
    def create_financial_report_tab(self, tabs):
        """إنشاء تبويب التقرير المالي"""
        report_widget = QWidget()
        report_layout = QVBoxLayout(report_widget)
        
        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setFont(QFont("Calibri", 11))
        self.report_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        report_layout.addWidget(self.report_text)
        
        tabs.addTab(report_widget, "📊 التقرير المالي")
    
    def create_charts_tab(self, tabs):
        """إنشاء تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)
        
        # أزرار التحكم في الرسوم البيانية
        chart_controls = QHBoxLayout()
        
        revenue_chart_btn = QPushButton("📈 رسم الإيرادات")
        revenue_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        revenue_chart_btn.setStyleSheet(self.get_button_style("#27ae60"))
        revenue_chart_btn.clicked.connect(self.show_revenue_chart)
        
        expense_chart_btn = QPushButton("📉 رسم المصاريف")
        expense_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        expense_chart_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        expense_chart_btn.clicked.connect(self.show_expense_chart)
        
        profit_chart_btn = QPushButton("💰 رسم الأرباح")
        profit_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        profit_chart_btn.setStyleSheet(self.get_button_style("#f39c12"))
        profit_chart_btn.clicked.connect(self.show_profit_chart)
        
        chart_controls.addWidget(revenue_chart_btn)
        chart_controls.addWidget(expense_chart_btn)
        chart_controls.addWidget(profit_chart_btn)
        chart_controls.addStretch()
        
        charts_layout.addLayout(chart_controls)
        
        # منطقة عرض الرسم البياني
        self.chart_widget = QWidget()
        self.chart_widget.setMinimumHeight(500)
        self.chart_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        charts_layout.addWidget(self.chart_widget)
        
        tabs.addTab(charts_widget, "📈 الرسوم البيانية")
    
    def create_analysis_tab(self, tabs):
        """إنشاء تبويب التحليل المقارن"""
        analysis_widget = QWidget()
        analysis_layout = QVBoxLayout(analysis_widget)
        
        # جدول التحليل المقارن
        self.analysis_table = QTableWidget()
        self.analysis_table.setColumnCount(5)
        self.analysis_table.setHorizontalHeaderLabels([
            "البند", "الفترة الحالية", "الفترة السابقة", "الفرق", "نسبة التغيير"
        ])
        
        # تنسيق الجدول
        self.analysis_table.setFont(QFont("Calibri", 10))
        self.analysis_table.setAlternatingRowColors(True)
        self.analysis_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.analysis_table.horizontalHeader().setStretchLastSection(True)
        
        # تنسيق رأس الجدول - برتقالي مثل archived_accounts_window
        header = self.analysis_table.horizontalHeader()
        header.setFont(QFont("Calibri", 11, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)
        
        analysis_layout.addWidget(self.analysis_table)
        
        tabs.addTab(analysis_widget, "📊 التحليل المقارن")

    def get_input_style(self):
        """تنسيق حقول الإدخال"""
        return """
            QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 11px;
            }
            QSpinBox:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_combo_style(self):
        """تنسيق القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 11px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
            }
        """

    def get_button_style(self, color):
        """تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, hex_color, factor=0.9):
        """تغميق اللون"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def report_type_changed(self):
        """تغيير نوع التقرير"""
        report_type = self.report_type_combo.currentText()

        if report_type == "شهري":
            self.month_label.show()
            self.month_combo.show()
            self.quarter_label.hide()
            self.quarter_combo.hide()
        elif report_type == "فصلي":
            self.month_label.hide()
            self.month_combo.hide()
            self.quarter_label.show()
            self.quarter_combo.show()
        else:  # سنوي
            self.month_label.hide()
            self.month_combo.hide()
            self.quarter_label.hide()
            self.quarter_combo.hide()

    def load_report_data(self):
        """تحميل بيانات التقرير"""
        try:
            report_type = self.report_type_combo.currentText()
            year = self.year_spin.value()
            month = None
            quarter = None

            if report_type == "شهري":
                month = self.month_combo.currentIndex() + 1
            elif report_type == "فصلي":
                quarter = self.quarter_combo.currentIndex() + 1

            # جلب بيانات الإيرادات والمصاريف
            revenue_data = self.manager.get_revenue_data(year, month, quarter)
            expense_data = self.manager.get_expense_data(year, month, quarter)

            # إنشاء التقرير
            self.generate_profit_loss_report(revenue_data, expense_data, year, month, quarter)

            # تحديث شريط الحالة
            period_text = ""
            if month:
                months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                period_text = f"شهر {months[month-1]} {year}"
            elif quarter:
                period_text = f"الفصل {quarter} من سنة {year}"
            else:
                period_text = f"سنة {year}"

            self.statusBar().showMessage(f"تم تحميل تقرير الأرباح والخسائر لـ {period_text}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التقرير: {str(e)}")

    def generate_profit_loss_report(self, revenue_data, expense_data, year, month=None, quarter=None):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            # تحديد الفترة
            period_text = ""
            if month:
                months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                         "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
                period_text = f"شهر {months[month-1]} {year}"
            elif quarter:
                period_text = f"الفصل {quarter} من سنة {year}"
            else:
                period_text = f"سنة {year}"

            # بناء التقرير
            report = f"📈 تقرير الأرباح والخسائر (P&L)\n"
            report += f"الفترة: {period_text}\n"
            report += f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            report += "=" * 60 + "\n\n"

            # قسم الإيرادات
            report += "💰 الإيرادات:\n"
            report += "-" * 30 + "\n"

            total_revenue = 0
            for revenue_type, amount in revenue_data.items():
                report += f"   {revenue_type}: {amount:,.2f} درهم\n"
                total_revenue += amount

            report += "-" * 30 + "\n"
            report += f"   إجمالي الإيرادات: {total_revenue:,.2f} درهم\n\n"

            # قسم المصاريف
            report += "💸 المصاريف:\n"
            report += "-" * 30 + "\n"

            total_expenses = 0
            for expense_type, amount in expense_data.items():
                report += f"   {expense_type}: {amount:,.2f} درهم\n"
                total_expenses += amount

            report += "-" * 30 + "\n"
            report += f"   إجمالي المصاريف: {total_expenses:,.2f} درهم\n\n"

            # حساب الربح/الخسارة
            net_profit = total_revenue - total_expenses

            report += "📊 النتيجة النهائية:\n"
            report += "=" * 30 + "\n"

            if net_profit > 0:
                report += f"   الربح الصافي: {net_profit:,.2f} درهم ✅\n"
                profit_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
                report += f"   هامش الربح: {profit_margin:.2f}%\n"
            elif net_profit < 0:
                report += f"   الخسارة الصافية: {abs(net_profit):,.2f} درهم ❌\n"
                loss_margin = (abs(net_profit) / total_revenue * 100) if total_revenue > 0 else 0
                report += f"   نسبة الخسارة: {loss_margin:.2f}%\n"
            else:
                report += f"   التوازن: {net_profit:,.2f} درهم ⚖️\n"
                report += f"   الحالة: متوازن (لا ربح ولا خسارة)\n"

            # إضافة مؤشرات أداء إضافية
            report += "\n📈 مؤشرات الأداء:\n"
            report += "-" * 30 + "\n"

            if total_revenue > 0:
                expense_ratio = (total_expenses / total_revenue * 100)
                report += f"   نسبة المصاريف إلى الإيرادات: {expense_ratio:.2f}%\n"

                if expense_ratio < 70:
                    report += f"   تقييم الأداء: ممتاز ✅\n"
                elif expense_ratio < 85:
                    report += f"   تقييم الأداء: جيد 👍\n"
                elif expense_ratio < 95:
                    report += f"   تقييم الأداء: مقبول ⚠️\n"
                else:
                    report += f"   تقييم الأداء: يحتاج تحسين ❌\n"

            self.report_text.setPlainText(report)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def show_revenue_chart(self):
        """عرض رسم بياني للإيرادات"""
        QMessageBox.information(self, "قريباً", "رسم الإيرادات سيكون متاحاً قريباً")

    def show_expense_chart(self):
        """عرض رسم بياني للمصاريف"""
        QMessageBox.information(self, "قريباً", "رسم المصاريف سيكون متاحاً قريباً")

    def show_profit_chart(self):
        """عرض رسم بياني للأرباح"""
        QMessageBox.information(self, "قريباً", "رسم الأرباح سيكون متاحاً قريباً")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "طباعة التقرير ستكون متاحة قريباً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = ProfitLossWindow()
    window.show()

    sys.exit(app.exec_())
