# ================= نافذة إعداد الموازنة السنوية =================
# ملف منفصل لإدارة الموازنة السنوية للمؤسسة التعليمية
# يحتوي على: تخطيط الإيرادات والمصاريف، مقارنة المتوقع مع الفعلي، أشرطة التقدم

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# تعيين الخط العربي لـ matplotlib
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

class BudgetManager:
    """مدير الموازنة السنوية"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول الموازنة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول الموازنة السنوية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الموازنة_السنوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة INTEGER NOT NULL,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    المبلغ_المتوقع REAL NOT NULL,
                    المبلغ_الفعلي REAL DEFAULT 0,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول بنود الموازنة الافتراضية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS بنود_الموازنة_الافتراضية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    وصف_البند TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)
            
            # إدراج البنود الافتراضية
            default_budget_items = [
                # الإيرادات
                ('إيرادات', 'رسوم التسجيل', 'رسوم تسجيل التلاميذ الجدد'),
                ('إيرادات', 'الواجبات الشهرية', 'الرسوم الشهرية للتلاميذ'),
                ('إيرادات', 'رسوم إضافية', 'رسوم الأنشطة والخدمات الإضافية'),
                ('إيرادات', 'منح ودعم', 'المنح والدعم من الجهات المختلفة'),
                ('إيرادات', 'إيرادات أخرى', 'إيرادات متنوعة أخرى'),
                
                # المصاريف
                ('مصاريف', 'رواتب المدرسين', 'رواتب الكادر التدريسي'),
                ('مصاريف', 'رواتب الإداريين', 'رواتب الكادر الإداري'),
                ('مصاريف', 'إيجار المباني', 'تكلفة إيجار المرافق'),
                ('مصاريف', 'فواتير الخدمات', 'كهرباء، ماء، هاتف، إنترنت'),
                ('مصاريف', 'المعدات التعليمية', 'شراء وصيانة المعدات'),
                ('مصاريف', 'القرطاسية', 'المواد المكتبية والتعليمية'),
                ('مصاريف', 'النقل', 'مصاريف النقل والمواصلات'),
                ('مصاريف', 'التأمين', 'أقساط التأمين المختلفة'),
                ('مصاريف', 'الصيانة', 'صيانة المباني والمرافق'),
                ('مصاريف', 'التسويق', 'الدعاية والإعلان'),
                ('مصاريف', 'مصاريف أخرى', 'مصاريف متنوعة أخرى')
            ]
            
            cursor.executemany("""
                INSERT OR IGNORE INTO بنود_الموازنة_الافتراضية (نوع_البند, اسم_البند, وصف_البند) 
                VALUES (?, ?, ?)
            """, default_budget_items)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات الموازنة: {str(e)}")

class BudgetPlanningWindow(QMainWindow):
    """نافذة إعداد الموازنة السنوية"""
    
    def __init__(self):
        super().__init__()
        self.manager = BudgetManager()
        self.current_year = datetime.now().year
        self.init_ui()
        self.load_budget_items()
        self.load_budget_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 إعداد الموازنة السنوية")
        self.setGeometry(100, 100, 1600, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        self.center_window(self)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان الرئيسي
        title_label = QLabel("📊 نظام إعداد الموازنة السنوية")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # شريط أدوات السنة
        year_layout = QHBoxLayout()
        
        year_label = QLabel("📅 السنة:")
        year_label.setFont(QFont("Calibri", 12, QFont.Bold))
        
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setFont(QFont("Calibri", 12))
        self.year_spin.setStyleSheet(self.get_input_style())
        self.year_spin.valueChanged.connect(self.year_changed)
        
        load_btn = QPushButton("🔄 تحميل بيانات السنة")
        load_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        load_btn.setStyleSheet(self.get_button_style("#3498db"))
        load_btn.clicked.connect(self.load_budget_data)
        
        year_layout.addWidget(year_label)
        year_layout.addWidget(self.year_spin)
        year_layout.addWidget(load_btn)
        year_layout.addStretch()
        
        main_layout.addLayout(year_layout)
        
        # تخطيط أفقي للمحتوى
        content_layout = QHBoxLayout()
        main_layout.addLayout(content_layout)
        
        # الجانب الأيسر - نموذج إدخال البند
        self.create_budget_form(content_layout)
        
        # الجانب الأيمن - جدول الموازنة والرسوم البيانية
        self.create_budget_display(content_layout)
        
        # شريط الحالة
        self.statusBar().showMessage(f"جاهز لإعداد موازنة السنة {self.current_year}")
        self.statusBar().setFont(QFont("Calibri", 10))
    
    def create_budget_form(self, parent_layout):
        """إنشاء نموذج إدخال بند الموازنة"""
        form_group = QGroupBox("📝 إضافة/تعديل بند الموازنة")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #27ae60;
            }
        """)
        form_group.setMaximumWidth(450)
        
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        
        # نوع البند
        type_layout = QFormLayout()
        self.item_type_combo = QComboBox()
        self.item_type_combo.addItems(["إيرادات", "مصاريف"])
        self.item_type_combo.setFont(QFont("Calibri", 12))
        self.item_type_combo.setStyleSheet(self.get_combo_style())
        self.item_type_combo.currentTextChanged.connect(self.type_changed)
        type_layout.addRow(self.create_label("📋 نوع البند:"), self.item_type_combo)
        form_layout.addLayout(type_layout)
        
        # اسم البند
        name_layout = QFormLayout()
        self.item_name_combo = QComboBox()
        self.item_name_combo.setEditable(True)
        self.item_name_combo.setFont(QFont("Calibri", 12))
        self.item_name_combo.setStyleSheet(self.get_combo_style())
        name_layout.addRow(self.create_label("📝 اسم البند:"), self.item_name_combo)
        form_layout.addLayout(name_layout)
        
        # المبلغ المتوقع
        expected_layout = QFormLayout()
        self.expected_amount_input = QLineEdit()
        self.expected_amount_input.setPlaceholderText("أدخل المبلغ المتوقع...")
        self.expected_amount_input.setFont(QFont("Calibri", 12))
        self.expected_amount_input.setStyleSheet(self.get_input_style())
        expected_layout.addRow(self.create_label("💰 المبلغ المتوقع:"), self.expected_amount_input)
        form_layout.addLayout(expected_layout)
        
        # المبلغ الفعلي
        actual_layout = QFormLayout()
        self.actual_amount_input = QLineEdit()
        self.actual_amount_input.setPlaceholderText("المبلغ الفعلي (اختياري)...")
        self.actual_amount_input.setFont(QFont("Calibri", 12))
        self.actual_amount_input.setStyleSheet(self.get_input_style())
        actual_layout.addRow(self.create_label("💵 المبلغ الفعلي:"), self.actual_amount_input)
        form_layout.addLayout(actual_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_btn = QPushButton("➕ إضافة البند")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet(self.get_button_style("#27ae60"))
        add_btn.clicked.connect(self.add_budget_item)
        
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        clear_btn.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(clear_btn)
        form_layout.addLayout(buttons_layout)
        
        parent_layout.addWidget(form_group)

    def create_budget_display(self, parent_layout):
        """إنشاء منطقة عرض الموازنة والرسوم البيانية"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)

        # تبويبات العرض
        tabs = QTabWidget()
        tabs.setFont(QFont("Calibri", 11, QFont.Bold))

        # تبويب جدول الموازنة
        self.create_budget_table_tab(tabs)

        # تبويب الرسوم البيانية
        self.create_charts_tab(tabs)

        # تبويب التقارير
        self.create_reports_tab(tabs)

        display_layout.addWidget(tabs)
        parent_layout.addWidget(display_widget)

    def create_budget_table_tab(self, tabs):
        """إنشاء تبويب جدول الموازنة"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # أدوات التحكم
        controls_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setFont(QFont("Calibri", 11, QFont.Bold))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في بنود الموازنة...")
        self.search_input.setFont(QFont("Calibri", 11))
        self.search_input.setStyleSheet(self.get_input_style())
        self.search_input.textChanged.connect(self.filter_budget_items)

        # فلتر النوع
        type_filter_label = QLabel("📋 النوع:")
        type_filter_label.setFont(QFont("Calibri", 11, QFont.Bold))
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["جميع البنود", "إيرادات", "مصاريف"])
        self.type_filter_combo.setFont(QFont("Calibri", 11))
        self.type_filter_combo.setStyleSheet(self.get_combo_style())
        self.type_filter_combo.currentTextChanged.connect(self.filter_budget_items)

        controls_layout.addWidget(search_label)
        controls_layout.addWidget(self.search_input)
        controls_layout.addWidget(type_filter_label)
        controls_layout.addWidget(self.type_filter_combo)
        controls_layout.addStretch()

        table_layout.addLayout(controls_layout)

        # الجدول
        self.budget_table = QTableWidget()
        self.budget_table.setColumnCount(7)
        self.budget_table.setHorizontalHeaderLabels([
            "ID", "نوع البند", "اسم البند", "المبلغ المتوقع",
            "المبلغ الفعلي", "النسبة المحققة", "الفرق"
        ])

        # تنسيق الجدول
        self.budget_table.setFont(QFont("Calibri", 10))
        self.budget_table.setAlternatingRowColors(True)
        self.budget_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.budget_table.horizontalHeader().setStretchLastSection(True)

        # تنسيق رأس الجدول - برتقالي مثل archived_accounts_window
        header = self.budget_table.horizontalHeader()
        header.setFont(QFont("Calibri", 11, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # قائمة السياق
        self.budget_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.budget_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.budget_table)

        tabs.addTab(table_widget, "📊 جدول الموازنة")

    def create_charts_tab(self, tabs):
        """إنشاء تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)

        # أزرار التحكم في الرسوم البيانية
        chart_controls = QHBoxLayout()

        revenue_chart_btn = QPushButton("📈 رسم الإيرادات")
        revenue_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        revenue_chart_btn.setStyleSheet(self.get_button_style("#27ae60"))
        revenue_chart_btn.clicked.connect(self.show_revenue_chart)

        expense_chart_btn = QPushButton("📉 رسم المصاريف")
        expense_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        expense_chart_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        expense_chart_btn.clicked.connect(self.show_expense_chart)

        comparison_chart_btn = QPushButton("📊 رسم المقارنة")
        comparison_chart_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        comparison_chart_btn.setStyleSheet(self.get_button_style("#3498db"))
        comparison_chart_btn.clicked.connect(self.show_comparison_chart)

        chart_controls.addWidget(revenue_chart_btn)
        chart_controls.addWidget(expense_chart_btn)
        chart_controls.addWidget(comparison_chart_btn)
        chart_controls.addStretch()

        charts_layout.addLayout(chart_controls)

        # منطقة عرض الرسم البياني
        self.chart_widget = QWidget()
        self.chart_widget.setMinimumHeight(400)
        self.chart_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        charts_layout.addWidget(self.chart_widget)

        tabs.addTab(charts_widget, "📈 الرسوم البيانية")

    def create_reports_tab(self, tabs):
        """إنشاء تبويب التقارير"""
        reports_widget = QWidget()
        reports_layout = QVBoxLayout(reports_widget)

        # أزرار التقارير
        reports_controls = QHBoxLayout()

        summary_btn = QPushButton("📋 تقرير الملخص")
        summary_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        summary_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        summary_btn.clicked.connect(self.generate_summary_report)

        detailed_btn = QPushButton("📄 تقرير مفصل")
        detailed_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        detailed_btn.setStyleSheet(self.get_button_style("#34495e"))
        detailed_btn.clicked.connect(self.generate_detailed_report)

        export_btn = QPushButton("💾 تصدير البيانات")
        export_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        export_btn.setStyleSheet(self.get_button_style("#f39c12"))
        export_btn.clicked.connect(self.export_budget_data)

        reports_controls.addWidget(summary_btn)
        reports_controls.addWidget(detailed_btn)
        reports_controls.addWidget(export_btn)
        reports_controls.addStretch()

        reports_layout.addLayout(reports_controls)

        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setFont(QFont("Calibri", 11))
        self.report_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        reports_layout.addWidget(self.report_text)

        tabs.addTab(reports_widget, "📊 التقارير")

    def create_label(self, text):
        """إنشاء تسمية منسقة"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 11, QFont.Bold))
        label.setStyleSheet("color: #2c3e50; margin: 5px;")
        return label

    def get_input_style(self):
        """تنسيق حقول الإدخال"""
        return """
            QLineEdit, QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus, QSpinBox:focus {
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }
        """

    def get_combo_style(self):
        """تنسيق القوائم المنسدلة"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 12px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
            }
        """

    def get_button_style(self, color):
        """تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, hex_color, factor=0.9):
        """تغميق اللون"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    def year_changed(self):
        """تغيير السنة"""
        self.current_year = self.year_spin.value()
        self.statusBar().showMessage(f"تم تغيير السنة إلى {self.current_year}")

    def type_changed(self):
        """تغيير نوع البند"""
        self.load_budget_items()

    def load_budget_items(self):
        """تحميل بنود الموازنة حسب النوع"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            item_type = self.item_type_combo.currentText()
            cursor.execute("""
                SELECT اسم_البند FROM بنود_الموازنة_الافتراضية
                WHERE نوع_البند = ? AND نشط = 1
                ORDER BY اسم_البند
            """, (item_type,))
            items = cursor.fetchall()

            self.item_name_combo.clear()
            for item_row in items:
                self.item_name_combo.addItem(item_row[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بنود الموازنة: {str(e)}")

    def load_budget_data(self):
        """تحميل بيانات الموازنة للسنة المحددة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي
                FROM الموازنة_السنوية
                WHERE السنة = ?
                ORDER BY نوع_البند, اسم_البند
            """, (self.current_year,))
            budget_data = cursor.fetchall()

            self.budget_table.setRowCount(len(budget_data))

            for row, budget_item in enumerate(budget_data):
                item_id, item_type, item_name, expected, actual = budget_item

                # حساب النسبة والفرق
                if expected and expected > 0:
                    percentage = (actual / expected) * 100 if actual else 0
                    difference = actual - expected if actual else -expected
                else:
                    percentage = 0
                    difference = actual if actual else 0

                # تعبئة الجدول
                items = [
                    str(item_id),
                    item_type,
                    item_name,
                    f"{expected:,.2f} درهم",
                    f"{actual:,.2f} درهم" if actual else "0.00 درهم",
                    f"{percentage:.1f}%",
                    f"{difference:,.2f} درهم"
                ]

                for col, value in enumerate(items):
                    item = QTableWidgetItem(value)
                    item.setFont(QFont("Calibri", 10))

                    # تلوين حسب النوع والحالة
                    if col == 1:  # نوع البند
                        if item_type == "إيرادات":
                            item.setBackground(QColor("#d5f4e6"))
                        else:
                            item.setBackground(QColor("#ffeaa7"))
                    elif col == 5:  # النسبة المحققة
                        if percentage >= 100:
                            item.setBackground(QColor("#00b894"))
                            item.setForeground(QColor("white"))
                        elif percentage >= 75:
                            item.setBackground(QColor("#fdcb6e"))
                        else:
                            item.setBackground(QColor("#e17055"))
                            item.setForeground(QColor("white"))

                    self.budget_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(budget_data)} بند للسنة {self.current_year}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموازنة: {str(e)}")

    def add_budget_item(self):
        """إضافة بند موازنة جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.item_name_combo.currentText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البند")
                return

            if not self.expected_amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المتوقع")
                return

            try:
                expected_amount = float(self.expected_amount_input.text().strip())
                if expected_amount < 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ المتوقع أكبر من أو يساوي صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ متوقع صحيح")
                return

            actual_amount = 0
            if self.actual_amount_input.text().strip():
                try:
                    actual_amount = float(self.actual_amount_input.text().strip())
                    if actual_amount < 0:
                        QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ الفعلي أكبر من أو يساوي صفر")
                        return
                except ValueError:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ فعلي صحيح")
                    return

            # إدراج البند في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود البند مسبقاً
            cursor.execute("""
                SELECT id FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = ? AND اسم_البند = ?
            """, (self.current_year, self.item_type_combo.currentText(), self.item_name_combo.currentText()))

            existing = cursor.fetchone()

            if existing:
                # تحديث البند الموجود
                cursor.execute("""
                    UPDATE الموازنة_السنوية
                    SET المبلغ_المتوقع = ?, المبلغ_الفعلي = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (expected_amount, actual_amount, existing[0]))
                message = "تم تحديث البند بنجاح"
            else:
                # إضافة بند جديد
                cursor.execute("""
                    INSERT INTO الموازنة_السنوية (السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.current_year,
                    self.item_type_combo.currentText(),
                    self.item_name_combo.currentText(),
                    expected_amount,
                    actual_amount
                ))
                message = "تم إضافة البند بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", message)
            self.clear_form()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.item_type_combo.setCurrentIndex(0)
        self.item_name_combo.setCurrentIndex(0)
        self.expected_amount_input.clear()
        self.actual_amount_input.clear()

    def filter_budget_items(self):
        """تصفية بنود الموازنة"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()

        for row in range(self.budget_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.budget_table.columnCount()):
                    item = self.budget_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع البنود":
                type_item = self.budget_table.item(row, 1)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            self.budget_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.budget_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.budget_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_budget_item()
        elif action == delete_action:
            self.delete_budget_item()

    def edit_budget_item(self):
        """تعديل بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات البند
        item_id = self.budget_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM الموازنة_السنوية WHERE id = ?", (item_id,))
            budget_item = cursor.fetchone()

            if budget_item:
                # تعبئة النموذج بالبيانات الحالية
                type_index = self.item_type_combo.findText(budget_item[2])
                if type_index >= 0:
                    self.item_type_combo.setCurrentIndex(type_index)

                self.load_budget_items()  # تحديث قائمة الأسماء

                name_index = self.item_name_combo.findText(budget_item[3])
                if name_index >= 0:
                    self.item_name_combo.setCurrentIndex(name_index)
                else:
                    self.item_name_combo.setEditText(budget_item[3])

                self.expected_amount_input.setText(str(budget_item[4]))
                self.actual_amount_input.setText(str(budget_item[5]) if budget_item[5] else "")

                # حذف البند القديم
                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات البند للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة البند'.")

            conn.close()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل البند: {str(e)}")

    def delete_budget_item(self):
        """حذف بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        item_id = self.budget_table.item(current_row, 0).text()
        item_name = self.budget_table.item(current_row, 2).text()
        item_type = self.budget_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا البند؟\n\nالنوع: {item_type}\nالاسم: {item_name}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف البند بنجاح")
                self.load_budget_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف البند: {str(e)}")

    def show_revenue_chart(self):
        """عرض رسم بياني للإيرادات"""
        QMessageBox.information(self, "قريباً", "رسم الإيرادات سيكون متاحاً قريباً")

    def show_expense_chart(self):
        """عرض رسم بياني للمصاريف"""
        QMessageBox.information(self, "قريباً", "رسم المصاريف سيكون متاحاً قريباً")

    def show_comparison_chart(self):
        """عرض رسم بياني للمقارنة"""
        QMessageBox.information(self, "قريباً", "رسم المقارنة سيكون متاحاً قريباً")

    def generate_summary_report(self):
        """إنشاء تقرير الملخص"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # حساب الإجماليات
            cursor.execute("""
                SELECT نوع_البند, SUM(المبلغ_المتوقع), SUM(المبلغ_الفعلي)
                FROM الموازنة_السنوية
                WHERE السنة = ?
                GROUP BY نوع_البند
            """, (self.current_year,))

            totals = cursor.fetchall()

            report = f"📊 تقرير ملخص الموازنة للسنة {self.current_year}\n"
            report += "=" * 50 + "\n\n"

            total_expected_revenue = 0
            total_actual_revenue = 0
            total_expected_expense = 0
            total_actual_expense = 0

            for total_row in totals:
                item_type, expected, actual = total_row
                expected = expected or 0
                actual = actual or 0

                if item_type == "إيرادات":
                    total_expected_revenue = expected
                    total_actual_revenue = actual
                else:
                    total_expected_expense = expected
                    total_actual_expense = actual

                percentage = (actual / expected * 100) if expected > 0 else 0

                report += f"📋 {item_type}:\n"
                report += f"   المتوقع: {expected:,.2f} درهم\n"
                report += f"   الفعلي: {actual:,.2f} درهم\n"
                report += f"   النسبة المحققة: {percentage:.1f}%\n\n"

            # حساب الربح/الخسارة
            expected_profit = total_expected_revenue - total_expected_expense
            actual_profit = total_actual_revenue - total_actual_expense

            report += "💰 الربح/الخسارة:\n"
            report += f"   المتوقع: {expected_profit:,.2f} درهم\n"
            report += f"   الفعلي: {actual_profit:,.2f} درهم\n"

            if actual_profit > 0:
                report += "   الحالة: ربح ✅\n"
            elif actual_profit < 0:
                report += "   الحالة: خسارة ❌\n"
            else:
                report += "   الحالة: متوازن ⚖️\n"

            self.report_text.setPlainText(report)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الملخص: {str(e)}")

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل"""
        QMessageBox.information(self, "قريباً", "التقرير المفصل سيكون متاحاً قريباً")

    def export_budget_data(self):
        """تصدير بيانات الموازنة"""
        QMessageBox.information(self, "قريباً", "تصدير البيانات سيكون متاحاً قريباً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = BudgetPlanningWindow()
    window.show()

    sys.exit(app.exec_())
