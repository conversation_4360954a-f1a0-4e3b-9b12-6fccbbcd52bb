# ================= نافذة إعداد الموازنة السنوية =================
# ملف منفصل لإدارة الموازنة السنوية للمؤسسة التعليمية
# يحتوي على: تخطيط الإيرادات والمصاريف، مقارنة المتوقع مع الفعلي، أشرطة التقدم

import sys
import os
import sqlite3
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر. الرسوم البيانية ستكون معطلة.")

class BudgetManager:
    """مدير الموازنة السنوية"""
    
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول الموازنة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول الموازنة السنوية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الموازنة_السنوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة INTEGER NOT NULL,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    المبلغ_المتوقع REAL NOT NULL,
                    المبلغ_الفعلي REAL DEFAULT 0,
                    تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول بنود الموازنة الافتراضية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS بنود_الموازنة_الافتراضية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    نوع_البند TEXT NOT NULL,
                    اسم_البند TEXT NOT NULL,
                    وصف_البند TEXT,
                    نشط BOOLEAN DEFAULT 1
                )
            """)
            
            # إدراج البنود الافتراضية
            default_budget_items = [
                # الإيرادات
                ('إيرادات', 'رسوم التسجيل', 'رسوم تسجيل التلاميذ الجدد'),
                ('إيرادات', 'الواجبات الشهرية', 'الرسوم الشهرية للتلاميذ'),
                ('إيرادات', 'رسوم إضافية', 'رسوم الأنشطة والخدمات الإضافية'),
                ('إيرادات', 'منح ودعم', 'المنح والدعم من الجهات المختلفة'),
                ('إيرادات', 'إيرادات أخرى', 'إيرادات متنوعة أخرى'),
                
                # المصاريف
                ('مصاريف', 'رواتب المدرسين', 'رواتب الكادر التدريسي'),
                ('مصاريف', 'رواتب الإداريين', 'رواتب الكادر الإداري'),
                ('مصاريف', 'إيجار المباني', 'تكلفة إيجار المرافق'),
                ('مصاريف', 'فواتير الخدمات', 'كهرباء، ماء، هاتف، إنترنت'),
                ('مصاريف', 'المعدات التعليمية', 'شراء وصيانة المعدات'),
                ('مصاريف', 'القرطاسية', 'المواد المكتبية والتعليمية'),
                ('مصاريف', 'النقل', 'مصاريف النقل والمواصلات'),
                ('مصاريف', 'التأمين', 'أقساط التأمين المختلفة'),
                ('مصاريف', 'الصيانة', 'صيانة المباني والمرافق'),
                ('مصاريف', 'التسويق', 'الدعاية والإعلان'),
                ('مصاريف', 'مصاريف أخرى', 'مصاريف متنوعة أخرى')
            ]
            
            cursor.executemany("""
                INSERT OR IGNORE INTO بنود_الموازنة_الافتراضية (نوع_البند, اسم_البند, وصف_البند) 
                VALUES (?, ?, ?)
            """, default_budget_items)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء قاعدة بيانات الموازنة: {str(e)}")

class BudgetPlanningWindow(QMainWindow):
    """نافذة إعداد الموازنة السنوية"""
    
    def __init__(self):
        super().__init__()
        self.manager = BudgetManager()
        self.current_year = datetime.now().year
        self.init_ui()
        self.load_saved_budget_data()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            screen = QApplication.desktop().screenGeometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 إعداد الموازنة السنوية")
        self.showMaximized()  # فتح في كامل الشاشة
        self.setLayoutDirection(Qt.RightToLeft)

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("📊 إعداد الموازنة السنوية")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # تخطيط أفقي للأقسام الثلاثة
        sections_layout = QHBoxLayout()

        # قسم الإيرادات والمداخل
        self.create_revenue_section(sections_layout)

        # قسم المصاريف والنفقات
        self.create_expenses_section(sections_layout)

        # قسم الموازنة
        self.create_budget_section(sections_layout)

        main_layout.addLayout(sections_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage(f"جاهز - نظام إعداد الموازنة السنوية {self.current_year}")

    def create_revenue_section(self, sections_layout):
        """إنشاء قسم الإيرادات والمداخل"""
        revenue_group = QGroupBox("💰 الإيرادات والمداخل")
        revenue_group.setFont(QFont("Calibri", 14, QFont.Bold))
        revenue_layout = QVBoxLayout(revenue_group)

        # أنواع المداخل الثلاثة
        revenue_types = [
            ("واجبات التسجيل والاشتراكات", "registration_fees"),
            ("الواجبات الشهرية", "monthly_duties"),
            ("أخرى", "other_revenue")
        ]

        self.revenue_inputs = {}
        self.revenue_progress_bars = {}

        for revenue_name, revenue_key in revenue_types:
            # عنوان النوع
            type_label = QLabel(f"📋 {revenue_name}")
            type_label.setFont(QFont("Calibri", 13, QFont.Bold))
            revenue_layout.addWidget(type_label)

            # حقل إدخال المبلغ المتوقع
            amount_layout = QHBoxLayout()
            amount_label = QLabel("المبلغ المتوقع:")
            amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
            amount_layout.addWidget(amount_label)

            amount_input = QLineEdit()
            amount_input.setPlaceholderText("أدخل المبلغ المتوقع بالدرهم المغربي...")
            amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
            amount_input.setFixedWidth(50)  # عرض 50 نقطة
            amount_input.textChanged.connect(lambda text, key=revenue_key: self.update_revenue_progress(key))
            self.revenue_inputs[revenue_key] = amount_input
            amount_layout.addWidget(amount_input)

            revenue_layout.addLayout(amount_layout)

            # شريط التقدم
            progress_bar = QProgressBar()
            progress_bar.setFixedHeight(40)
            progress_bar.setFixedWidth(300)  # عرض 300 نقطة
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #8B4513;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    background-color: #8B4513;
                }
                QProgressBar::chunk {
                    background-color: #FFD700;
                    border-radius: 3px;
                }
            """)
            self.revenue_progress_bars[revenue_key] = progress_bar
            revenue_layout.addWidget(progress_bar)

            revenue_layout.addSpacing(10)

        # زر حفظ الإيرادات
        save_revenue_btn = QPushButton("💾 حفظ الإيرادات")
        save_revenue_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        save_revenue_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_revenue_btn.clicked.connect(self.save_revenue_data)
        revenue_layout.addWidget(save_revenue_btn)

        sections_layout.addWidget(revenue_group)

    def create_expenses_section(self, sections_layout):
        """إنشاء قسم المصاريف والنفقات"""
        expenses_group = QGroupBox("💸 المصاريف والنفقات")
        expenses_group.setFont(QFont("Calibri", 14, QFont.Bold))
        expenses_layout = QVBoxLayout(expenses_group)

        # منطقة التمرير للمصاريف
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.expenses_scroll_layout = QVBoxLayout(scroll_widget)

        self.expense_inputs = {}
        self.expense_progress_bars = {}

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setFixedHeight(400)
        expenses_layout.addWidget(scroll_area)

        # تحميل أنواع المصاريف من قاعدة البيانات
        self.load_expense_types()

        # زر حفظ المصاريف
        save_expenses_btn = QPushButton("💾 حفظ المصاريف")
        save_expenses_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        save_expenses_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        save_expenses_btn.clicked.connect(self.save_expenses_data)
        expenses_layout.addWidget(save_expenses_btn)

        sections_layout.addWidget(expenses_group)

    def create_budget_section(self, sections_layout):
        """إنشاء قسم الموازنة"""
        budget_group = QGroupBox("📊 الموازنة")
        budget_group.setFont(QFont("Calibri", 14, QFont.Bold))
        budget_layout = QVBoxLayout(budget_group)

        # ملخص الموازنة
        summary_label = QLabel("📈 ملخص الموازنة")
        summary_label.setFont(QFont("Calibri", 13, QFont.Bold))
        budget_layout.addWidget(summary_label)

        # منطقة عرض الملخص
        self.budget_summary = QTextEdit()
        self.budget_summary.setFont(QFont("Calibri", 12))
        self.budget_summary.setReadOnly(True)
        self.budget_summary.setFixedHeight(300)
        budget_layout.addWidget(self.budget_summary)

        # أزرار الموازنة
        budget_buttons_layout = QHBoxLayout()

        update_btn = QPushButton("🔄 تحديث الموازنة")
        update_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        update_btn.clicked.connect(self.update_budget_summary)
        budget_buttons_layout.addWidget(update_btn)

        export_btn = QPushButton("📄 تصدير التقرير")
        export_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        export_btn.clicked.connect(self.export_budget_report)
        budget_buttons_layout.addWidget(export_btn)

        budget_layout.addLayout(budget_buttons_layout)
        sections_layout.addWidget(budget_group)

    def create_budget_form(self, main_layout):
        """إنشاء نموذج إدخال بند الموازنة"""
        form_group = QGroupBox("📝 إدخال بند الموازنة")
        form_group.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout = QHBoxLayout(form_group)

        # السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(year_label)

        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2050)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setFont(QFont("Calibri", 12, QFont.Bold))
        self.year_spin.valueChanged.connect(self.year_changed)
        form_layout.addWidget(self.year_spin)

        # نوع البند
        type_label = QLabel("نوع البند:")
        type_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(type_label)

        self.item_type_combo = QComboBox()
        self.item_type_combo.addItems(["إيرادات", "مصاريف"])
        self.item_type_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.item_type_combo.currentTextChanged.connect(self.type_changed)
        form_layout.addWidget(self.item_type_combo)

        # اسم البند
        name_label = QLabel("اسم البند:")
        name_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(name_label)

        self.item_name_combo = QComboBox()
        self.item_name_combo.setEditable(True)
        self.item_name_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.item_name_combo)

        # المبلغ المتوقع
        expected_label = QLabel("المبلغ المتوقع:")
        expected_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(expected_label)

        self.expected_amount_input = QLineEdit()
        self.expected_amount_input.setPlaceholderText("المبلغ المتوقع...")
        self.expected_amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.expected_amount_input)

        # المبلغ الفعلي
        actual_label = QLabel("المبلغ الفعلي:")
        actual_label.setFont(QFont("Calibri", 14, QFont.Bold))
        form_layout.addWidget(actual_label)

        self.actual_amount_input = QLineEdit()
        self.actual_amount_input.setPlaceholderText("المبلغ الفعلي...")
        self.actual_amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        form_layout.addWidget(self.actual_amount_input)

        main_layout.addWidget(form_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر إضافة البند
        add_btn = QPushButton("➕ إضافة البند")
        add_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_btn.clicked.connect(self.add_budget_item)
        buttons_layout.addWidget(add_btn)

        # زر مسح الحقول
        clear_btn = QPushButton("🗑️ مسح الحقول")
        clear_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_budget_data)
        buttons_layout.addWidget(refresh_btn)

        # زر الإحصائيات
        stats_btn = QPushButton("📈 الإحصائيات")
        stats_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        stats_btn.clicked.connect(self.generate_summary_report)
        buttons_layout.addWidget(stats_btn)

        main_layout.addWidget(buttons_group)

    def create_search_group(self, main_layout):
        """إنشاء مجموعة البحث والتصفية"""
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)

        # البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في بنود الموازنة...")
        self.search_input.setFont(QFont("Calibri", 12, QFont.Bold))
        self.search_input.textChanged.connect(self.filter_budget_items)
        search_layout.addWidget(self.search_input)

        # فلتر النوع
        type_filter_label = QLabel("النوع:")
        type_filter_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(type_filter_label)

        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["جميع البنود", "إيرادات", "مصاريف"])
        self.type_filter_combo.setFont(QFont("Calibri", 12, QFont.Bold))
        self.type_filter_combo.currentTextChanged.connect(self.filter_budget_items)
        search_layout.addWidget(self.type_filter_combo)

        main_layout.addWidget(search_group)

    def create_budget_table(self, main_layout):
        """إنشاء جدول الموازنة"""
        table_group = QGroupBox("📊 بنود الموازنة")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.budget_table = QTableWidget()
        self.budget_table.setColumnCount(7)
        self.budget_table.setHorizontalHeaderLabels([
            "ID", "نوع البند", "اسم البند", "المبلغ المتوقع",
            "المبلغ الفعلي", "النسبة المحققة", "الفرق"
        ])

        # تنسيق رؤوس الجدول
        header = self.budget_table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #ff9800;
                color: white;
                padding: 8px;
                border: 1px solid #e67e22;
                font-weight: bold;
            }
        """)

        # تنسيق الجدول
        self.budget_table.setAlternatingRowColors(True)
        self.budget_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.budget_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.budget_table.setSortingEnabled(True)

        # تنسيق الجدول
        self.budget_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إضافة قائمة سياق للجدول
        self.budget_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.budget_table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.budget_table)
        main_layout.addWidget(table_group)





    def year_changed(self):
        """تغيير السنة"""
        self.current_year = self.year_spin.value()
        self.statusBar().showMessage(f"تم تغيير السنة إلى {self.current_year}")

    def type_changed(self):
        """تغيير نوع البند"""
        self.load_budget_items()

    def load_budget_items(self):
        """تحميل بنود الموازنة حسب النوع"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            item_type = self.item_type_combo.currentText()
            cursor.execute("""
                SELECT اسم_البند FROM بنود_الموازنة_الافتراضية
                WHERE نوع_البند = ? AND نشط = 1
                ORDER BY اسم_البند
            """, (item_type,))
            items = cursor.fetchall()

            self.item_name_combo.clear()
            for item_row in items:
                self.item_name_combo.addItem(item_row[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بنود الموازنة: {str(e)}")

    def load_budget_data(self):
        """تحميل بيانات الموازنة للسنة المحددة"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي
                FROM الموازنة_السنوية
                WHERE السنة = ?
                ORDER BY نوع_البند, اسم_البند
            """, (self.current_year,))
            budget_data = cursor.fetchall()

            self.budget_table.setRowCount(len(budget_data))

            for row, budget_item in enumerate(budget_data):
                item_id, item_type, item_name, expected, actual = budget_item

                # حساب النسبة والفرق
                if expected and expected > 0:
                    percentage = (actual / expected) * 100 if actual else 0
                    difference = actual - expected if actual else -expected
                else:
                    percentage = 0
                    difference = actual if actual else 0

                # تعبئة الجدول
                items = [
                    str(item_id),
                    item_type,
                    item_name,
                    f"{expected:,.2f}",
                    f"{actual:,.2f}" if actual else "0.00",
                    f"{percentage:.1f}%",
                    f"{difference:,.2f}"
                ]

                for col, value in enumerate(items):
                    item = QTableWidgetItem(value)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 12, QFont.Bold))

                    # تلوين حسب النوع والحالة
                    if col == 1:  # نوع البند
                        if item_type == "إيرادات":
                            item.setBackground(QColor("#d5f4e6"))
                        else:
                            item.setBackground(QColor("#ffeaa7"))
                    elif col == 5:  # النسبة المحققة
                        if percentage >= 100:
                            item.setBackground(QColor("#00b894"))
                            item.setForeground(QColor("white"))
                        elif percentage >= 75:
                            item.setBackground(QColor("#fdcb6e"))
                        else:
                            item.setBackground(QColor("#e17055"))
                            item.setForeground(QColor("white"))

                    self.budget_table.setItem(row, col, item)

            conn.close()
            self.statusBar().showMessage(f"تم تحميل {len(budget_data)} بند للسنة {self.current_year}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموازنة: {str(e)}")
            self.budget_table.setRowCount(0)
            self.statusBar().showMessage("لا توجد بنود موازنة")

    def add_budget_item(self):
        """إضافة بند موازنة جديد"""
        try:
            # التحقق من صحة البيانات
            if not self.item_name_combo.currentText().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البند")
                return

            if not self.expected_amount_input.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال المبلغ المتوقع")
                return

            try:
                expected_amount = float(self.expected_amount_input.text().strip())
                if expected_amount < 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ المتوقع أكبر من أو يساوي صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ متوقع صحيح")
                return

            actual_amount = 0
            if self.actual_amount_input.text().strip():
                try:
                    actual_amount = float(self.actual_amount_input.text().strip())
                    if actual_amount < 0:
                        QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ الفعلي أكبر من أو يساوي صفر")
                        return
                except ValueError:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ فعلي صحيح")
                    return

            # إدراج البند في قاعدة البيانات
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود البند مسبقاً
            cursor.execute("""
                SELECT id FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = ? AND اسم_البند = ?
            """, (self.current_year, self.item_type_combo.currentText(), self.item_name_combo.currentText()))

            existing = cursor.fetchone()

            if existing:
                # تحديث البند الموجود
                cursor.execute("""
                    UPDATE الموازنة_السنوية
                    SET المبلغ_المتوقع = ?, المبلغ_الفعلي = ?, تاريخ_التحديث = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (expected_amount, actual_amount, existing[0]))
                message = "تم تحديث البند بنجاح"
            else:
                # إضافة بند جديد
                cursor.execute("""
                    INSERT INTO الموازنة_السنوية (السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    self.current_year,
                    self.item_type_combo.currentText(),
                    self.item_name_combo.currentText(),
                    expected_amount,
                    actual_amount
                ))
                message = "تم إضافة البند بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", message)
            self.clear_form()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة البند: {str(e)}")

    def clear_form(self):
        """مسح حقول النموذج"""
        self.item_type_combo.setCurrentIndex(0)
        if self.item_name_combo.count() > 0:
            self.item_name_combo.setCurrentIndex(0)
        self.expected_amount_input.clear()
        self.actual_amount_input.clear()

    def filter_budget_items(self):
        """تصفية بنود الموازنة"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter_combo.currentText()

        for row in range(self.budget_table.rowCount()):
            show_row = True

            # فلتر النص
            if search_text:
                row_text = ""
                for col in range(self.budget_table.columnCount()):
                    item = self.budget_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلتر النوع
            if type_filter and type_filter != "جميع البنود":
                type_item = self.budget_table.item(row, 1)
                if not type_item or type_item.text() != type_filter:
                    show_row = False

            self.budget_table.setRowHidden(row, not show_row)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.budget_table.itemAt(position) is None:
            return

        menu = QMenu(self)

        edit_action = menu.addAction("✏️ تعديل")
        delete_action = menu.addAction("🗑️ حذف")

        action = menu.exec_(self.budget_table.mapToGlobal(position))

        if action == edit_action:
            self.edit_budget_item()
        elif action == delete_action:
            self.delete_budget_item()

    def edit_budget_item(self):
        """تعديل بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        # جلب بيانات البند
        item_id = self.budget_table.item(current_row, 0).text()

        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM الموازنة_السنوية WHERE id = ?", (item_id,))
            budget_item = cursor.fetchone()

            if budget_item:
                # تعبئة النموذج بالبيانات الحالية
                type_index = self.item_type_combo.findText(budget_item[2])
                if type_index >= 0:
                    self.item_type_combo.setCurrentIndex(type_index)

                self.load_budget_items()  # تحديث قائمة الأسماء

                name_index = self.item_name_combo.findText(budget_item[3])
                if name_index >= 0:
                    self.item_name_combo.setCurrentIndex(name_index)
                else:
                    self.item_name_combo.setEditText(budget_item[3])

                self.expected_amount_input.setText(str(budget_item[4]))
                self.actual_amount_input.setText(str(budget_item[5]) if budget_item[5] else "")

                # حذف البند القديم
                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()

                QMessageBox.information(self, "تعديل", "تم تحميل بيانات البند للتعديل.\nيرجى تعديل البيانات والضغط على 'إضافة البند'.")

            conn.close()
            self.load_budget_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعديل البند: {str(e)}")

    def delete_budget_item(self):
        """حذف بند الموازنة"""
        current_row = self.budget_table.currentRow()
        if current_row < 0:
            return

        item_id = self.budget_table.item(current_row, 0).text()
        item_name = self.budget_table.item(current_row, 2).text()
        item_type = self.budget_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف هذا البند؟\n\nالنوع: {item_type}\nالاسم: {item_name}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = sqlite3.connect(self.manager.db_path)
                cursor = conn.cursor()

                cursor.execute("DELETE FROM الموازنة_السنوية WHERE id = ?", (item_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف البند بنجاح")
                self.load_budget_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف البند: {str(e)}")

    def show_revenue_chart(self):
        """عرض رسم بياني للإيرادات"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم الإيرادات سيكون متاحاً قريباً")

    def show_expense_chart(self):
        """عرض رسم بياني للمصاريف"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم المصاريف سيكون متاحاً قريباً")

    def show_comparison_chart(self):
        """عرض رسم بياني للمقارنة"""
        if not MATPLOTLIB_AVAILABLE:
            QMessageBox.warning(self, "مكتبة مفقودة",
                              "يرجى تثبيت matplotlib لعرض الرسوم البيانية:\n\npip install matplotlib")
            return
        QMessageBox.information(self, "قريباً", "رسم المقارنة سيكون متاحاً قريباً")

    def generate_summary_report(self):
        """إنشاء تقرير الملخص"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # حساب الإجماليات
            cursor.execute("""
                SELECT نوع_البند, SUM(المبلغ_المتوقع), SUM(المبلغ_الفعلي)
                FROM الموازنة_السنوية
                WHERE السنة = ?
                GROUP BY نوع_البند
            """, (self.current_year,))

            totals = cursor.fetchall()

            report = f"📊 تقرير ملخص الموازنة للسنة {self.current_year}\n"
            report += "=" * 50 + "\n\n"

            total_expected_revenue = 0
            total_actual_revenue = 0
            total_expected_expense = 0
            total_actual_expense = 0

            for total_row in totals:
                item_type, expected, actual = total_row
                expected = expected or 0
                actual = actual or 0

                if item_type == "إيرادات":
                    total_expected_revenue = expected
                    total_actual_revenue = actual
                else:
                    total_expected_expense = expected
                    total_actual_expense = actual

                percentage = (actual / expected * 100) if expected > 0 else 0

                report += f"📋 {item_type}:\n"
                report += f"   المتوقع: {expected:,.2f} درهم\n"
                report += f"   الفعلي: {actual:,.2f} درهم\n"
                report += f"   النسبة المحققة: {percentage:.1f}%\n\n"

            # حساب الربح/الخسارة
            expected_profit = total_expected_revenue - total_expected_expense
            actual_profit = total_actual_revenue - total_actual_expense

            report += "💰 الربح/الخسارة:\n"
            report += f"   المتوقع: {expected_profit:,.2f} درهم\n"
            report += f"   الفعلي: {actual_profit:,.2f} درهم\n"

            if actual_profit > 0:
                report += "   الحالة: ربح ✅\n"
            elif actual_profit < 0:
                report += "   الحالة: خسارة ❌\n"
            else:
                report += "   الحالة: متوازن ⚖️\n"

            self.report_text.setPlainText(report)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الملخص: {str(e)}")

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل"""
        QMessageBox.information(self, "قريباً", "التقرير المفصل سيكون متاحاً قريباً")

    def export_budget_data(self):
        """تصدير بيانات الموازنة"""
        QMessageBox.information(self, "قريباً", "تصدير البيانات سيكون متاحاً قريباً")

    def load_expense_types(self):
        """تحميل أنواع المصاريف من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT اسم_النوع FROM أنواع_المصاريف WHERE نشط = 1 ORDER BY اسم_النوع")
            expense_types = cursor.fetchall()

            # إنشاء حقول الإدخال لكل نوع مصروف
            for expense_type_row in expense_types:
                expense_type = expense_type_row[0]
                self.create_expense_input(expense_type)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل أنواع المصاريف: {str(e)}")
            # إنشاء أنواع افتراضية في حالة عدم وجود الجدول
            default_types = ["رواتب", "إيجار", "كهرباء", "مياه", "قرطاسية", "صيانة", "أخرى"]
            for expense_type in default_types:
                self.create_expense_input(expense_type)

    def create_expense_input(self, expense_type):
        """إنشاء حقل إدخال لنوع مصروف"""
        # عنوان النوع
        type_label = QLabel(f"📋 {expense_type}")
        type_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.expenses_scroll_layout.addWidget(type_label)

        # حقل إدخال المبلغ المتوقع
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ المتوقع:")
        amount_label.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_layout.addWidget(amount_label)

        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ المتوقع بالدرهم المغربي...")
        amount_input.setFont(QFont("Calibri", 12, QFont.Bold))
        amount_input.setFixedWidth(50)  # عرض 50 نقطة
        amount_input.textChanged.connect(lambda text, key=expense_type: self.update_expense_progress(key))
        self.expense_inputs[expense_type] = amount_input
        amount_layout.addWidget(amount_input)

        self.expenses_scroll_layout.addLayout(amount_layout)

        # شريط التقدم
        progress_bar = QProgressBar()
        progress_bar.setFixedHeight(40)
        progress_bar.setFixedWidth(300)  # عرض 300 نقطة
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #8B4513;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #8B4513;
            }
            QProgressBar::chunk {
                background-color: #FF0000;
                border-radius: 3px;
            }
        """)
        self.expense_progress_bars[expense_type] = progress_bar
        self.expenses_scroll_layout.addWidget(progress_bar)

        self.expenses_scroll_layout.addSpacing(10)

    def update_revenue_progress(self, revenue_key):
        """تحديث شريط تقدم الإيرادات"""
        try:
            expected_amount = float(self.revenue_inputs[revenue_key].text() or 0)
            actual_amount = self.get_actual_revenue(revenue_key)

            if expected_amount > 0:
                percentage = min(100, (actual_amount / expected_amount) * 100)
            else:
                percentage = 0

            progress_bar = self.revenue_progress_bars[revenue_key]
            progress_bar.setValue(int(percentage))
            progress_bar.setFormat(f"{actual_amount:,.0f} / {expected_amount:,.0f} ({percentage:.1f}%)")

        except (ValueError, ZeroDivisionError):
            pass

    def update_expense_progress(self, expense_type):
        """تحديث شريط تقدم المصاريف"""
        try:
            expected_amount = float(self.expense_inputs[expense_type].text() or 0)
            actual_amount = self.get_actual_expense(expense_type)

            if expected_amount > 0:
                percentage = min(100, (actual_amount / expected_amount) * 100)
            else:
                percentage = 0

            progress_bar = self.expense_progress_bars[expense_type]
            progress_bar.setValue(int(percentage))
            progress_bar.setFormat(f"{actual_amount:,.0f} / {expected_amount:,.0f} ({percentage:.1f}%)")

        except (ValueError, ZeroDivisionError):
            pass

    def get_actual_revenue(self, revenue_key):
        """الحصول على الإيرادات الفعلية"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            if revenue_key == "registration_fees":
                # جدول registration_fees يستخدم amount_paid و payment_date
                cursor.execute("SELECT SUM(amount_paid) FROM registration_fees WHERE strftime('%Y', payment_date) = ?", (str(self.current_year),))
            elif revenue_key == "monthly_duties":
                # جدول monthly_duties يستخدم amount_paid و payment_date
                cursor.execute("SELECT SUM(amount_paid) FROM monthly_duties WHERE strftime('%Y', payment_date) = ? AND payment_status != 'غير مدفوع'", (str(self.current_year),))
            else:  # other_revenue
                return 0  # يمكن إضافة جدول للإيرادات الأخرى لاحقاً

            result = cursor.fetchone()
            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب الإيرادات الفعلية: {str(e)}")
            return 0

    def get_actual_expense(self, expense_type):
        """الحصول على المصاريف الفعلية"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول المصاريف أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المصاريف'")
            if cursor.fetchone():
                cursor.execute("""
                    SELECT SUM(المبلغ) FROM المصاريف
                    WHERE نوع_المصروف = ? AND strftime('%Y', التاريخ) = ?
                """, (expense_type, str(self.current_year)))

                result = cursor.fetchone()
                conn.close()
                return result[0] if result and result[0] else 0
            else:
                conn.close()
                return 0

        except Exception as e:
            print(f"خطأ في جلب المصاريف الفعلية: {str(e)}")
            return 0

    def save_revenue_data(self):
        """حفظ بيانات الإيرادات"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            for revenue_key, input_field in self.revenue_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount > 0:
                        # حفظ في جدول الموازنة
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "إيرادات", revenue_key, expected_amount, self.get_actual_revenue(revenue_key)))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات الإيرادات بنجاح")
            self.update_all_progress_bars()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات الإيرادات: {str(e)}")

    def save_expenses_data(self):
        """حفظ بيانات المصاريف"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            for expense_type, input_field in self.expense_inputs.items():
                try:
                    expected_amount = float(input_field.text() or 0)
                    if expected_amount > 0:
                        # حفظ في جدول الموازنة
                        cursor.execute("""
                            INSERT OR REPLACE INTO الموازنة_السنوية
                            (السنة, نوع_البند, اسم_البند, المبلغ_المتوقع, المبلغ_الفعلي)
                            VALUES (?, ?, ?, ?, ?)
                        """, (self.current_year, "مصاريف", expense_type, expected_amount, self.get_actual_expense(expense_type)))
                except ValueError:
                    continue

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ بيانات المصاريف بنجاح")
            self.update_all_progress_bars()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات المصاريف: {str(e)}")

    def update_all_progress_bars(self):
        """تحديث جميع أشرطة التقدم"""
        # تحديث أشرطة الإيرادات
        for revenue_key in self.revenue_inputs.keys():
            self.update_revenue_progress(revenue_key)

        # تحديث أشرطة المصاريف
        for expense_type in self.expense_inputs.keys():
            self.update_expense_progress(expense_type)

    def update_budget_summary(self):
        """تحديث ملخص الموازنة"""
        try:
            # حساب إجمالي الإيرادات
            total_expected_revenue = sum(float(input_field.text() or 0) for input_field in self.revenue_inputs.values())
            total_actual_revenue = sum(self.get_actual_revenue(key) for key in self.revenue_inputs.keys())

            # حساب إجمالي المصاريف
            total_expected_expenses = sum(float(input_field.text() or 0) for input_field in self.expense_inputs.values())
            total_actual_expenses = sum(self.get_actual_expense(key) for key in self.expense_inputs.keys())

            # حساب الربح/الخسارة
            expected_profit = total_expected_revenue - total_expected_expenses
            actual_profit = total_actual_revenue - total_actual_expenses

            # إنشاء الملخص
            summary = f"""📊 ملخص الموازنة للسنة {self.current_year}
{'='*50}

💰 الإيرادات:
   المتوقع: {total_expected_revenue:,.2f} درهم
   الفعلي: {total_actual_revenue:,.2f} درهم
   النسبة: {(total_actual_revenue/total_expected_revenue*100) if total_expected_revenue > 0 else 0:.1f}%

💸 المصاريف:
   المتوقع: {total_expected_expenses:,.2f} درهم
   الفعلي: {total_actual_expenses:,.2f} درهم
   النسبة: {(total_actual_expenses/total_expected_expenses*100) if total_expected_expenses > 0 else 0:.1f}%

📈 الربح/الخسارة:
   المتوقع: {expected_profit:,.2f} درهم
   الفعلي: {actual_profit:,.2f} درهم

   الحالة: {"ربح ✅" if actual_profit > 0 else "خسارة ❌" if actual_profit < 0 else "متوازن ⚖️"}
"""

            self.budget_summary.setPlainText(summary)
            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث ملخص الموازنة: {str(e)}")

    def export_budget_report(self):
        """تصدير تقرير الموازنة"""
        QMessageBox.information(self, "قريباً", "تصدير التقرير سيكون متاحاً قريباً")

    def load_saved_budget_data(self):
        """تحميل البيانات المحفوظة في حقول الإدخال"""
        try:
            conn = sqlite3.connect(self.manager.db_path)
            cursor = conn.cursor()

            # تحميل بيانات الإيرادات
            cursor.execute("""
                SELECT اسم_البند, المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'إيرادات'
            """, (self.current_year,))

            revenue_data = cursor.fetchall()
            for item_name, expected_amount in revenue_data:
                if item_name in self.revenue_inputs:
                    self.revenue_inputs[item_name].setText(str(expected_amount))

            # تحميل بيانات المصاريف
            cursor.execute("""
                SELECT اسم_البند, المبلغ_المتوقع FROM الموازنة_السنوية
                WHERE السنة = ? AND نوع_البند = 'مصاريف'
            """, (self.current_year,))

            expense_data = cursor.fetchall()
            for item_name, expected_amount in expense_data:
                if item_name in self.expense_inputs:
                    self.expense_inputs[item_name].setText(str(expected_amount))

            conn.close()

            # تحديث أشرطة التقدم
            self.update_all_progress_bars()

            # تحديث ملخص الموازنة
            self.update_budget_summary()

        except Exception as e:
            print(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = BudgetPlanningWindow()
    window.show()

    sys.exit(app.exec_())
